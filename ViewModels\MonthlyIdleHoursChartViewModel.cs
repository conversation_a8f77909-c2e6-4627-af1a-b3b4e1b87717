using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Timers;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using ZoomableApp.Services;
using ZoomableApp.Models;

namespace ZoomableApp.ViewModels
{
    /// <summary>
    /// ViewModel cho Monthly Idle Hours Chart (Panel 3 - Right)
    /// Hiển thị thời gian nghỉ hàng tháng dưới dạng half pie chart
    /// </summary>
    public class MonthlyIdleHoursChartViewModel : INotifyPropertyChanged
    {
        private readonly MockDashboardDataService _mockDataService;
        private System.Timers.Timer _refreshTimer;
        private string _lastUpdated = "";

        public event PropertyChangedEventHandler? PropertyChanged;

        public ObservableCollection<ISeries> Series { get; set; } = new();
        
        public string LastUpdated
        {
            get => _lastUpdated;
            set
            {
                _lastUpdated = value;
                OnPropertyChanged(nameof(LastUpdated));
            }
        }

        public double IdleHours { get; private set; }
        public double WorkingHours { get; private set; }
        public double UtilizationRate { get; private set; }
        public int DaysInMonth { get; private set; }

        public MonthlyIdleHoursChartViewModel()
        {
            _mockDataService = new MockDashboardDataService();
            DaysInMonth = DateTime.DaysInMonth(DateTime.Now.Year, DateTime.Now.Month);
            InitializeTimer();
            LoadDataAsync();
        }

        private void InitializeTimer()
        {
            var interval = ConfigurationService.GetDashboardRefreshInterval();
            _refreshTimer = new System.Timers.Timer(interval);
            _refreshTimer.Elapsed += async (s, e) => await LoadDataAsync();
            _refreshTimer.Start();
        }

        private async Task LoadDataAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    // Kiểm tra PLC mode
                    var plcMode = ConfigurationService.GetPlcMode();
                    
                    if (plcMode == PlcMode.Mock)
                    {
                        LoadMockData();
                    }
                    else
                    {
                        // TODO: Load real data from PLC/Database
                        // Fallback to mock data for now
                        LoadMockData();
                    }

                    LastUpdated = DateTime.Now.ToString("HH:mm:ss");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"MonthlyIdleHoursChart: Error loading data: {ex.Message}");
                    LoadMockData(); // Fallback
                }
            });
        }

        private void LoadMockData()
        {
            var idleData = _mockDataService.GetMockIdleHoursData(false); // Monthly data
            
            IdleHours = idleData.IdleHours;
            WorkingHours = idleData.TotalHours - idleData.IdleHours;
            UtilizationRate = idleData.UtilizationRate;

            Series.Clear();

            // Tạo half pie chart cho monthly idle hours
            Series.Add(new PieSeries<double>
            {
                Values = new[] { WorkingHours },
                Name = $"Hoạt động ({WorkingHours:F0}h)",
                Fill = new SolidColorPaint(SKColors.Blue),
                Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                DataLabelsPaint = new SolidColorPaint(SKColors.White),
                DataLabelsSize = 12,
                DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                DataLabelsFormatter = point => $"{point.PrimaryValue:F0}h"
            });

            Series.Add(new PieSeries<double>
            {
                Values = new[] { IdleHours },
                Name = $"Nghỉ ({IdleHours:F0}h)",
                Fill = new SolidColorPaint(SKColors.Orange),
                Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                DataLabelsPaint = new SolidColorPaint(SKColors.White),
                DataLabelsSize = 12,
                DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                DataLabelsFormatter = point => $"{point.PrimaryValue:F0}h"
            });

            // Update properties for binding
            OnPropertyChanged(nameof(IdleHours));
            OnPropertyChanged(nameof(WorkingHours));
            OnPropertyChanged(nameof(UtilizationRate));

            System.Diagnostics.Debug.WriteLine($"MonthlyIdleHours: Working={WorkingHours:F0}h, Idle={IdleHours:F0}h, Utilization={UtilizationRate:F1}%");
        }

        /// <summary>
        /// Force refresh data
        /// </summary>
        public async Task RefreshAsync()
        {
            await LoadDataAsync();
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void Dispose()
        {
            _refreshTimer?.Stop();
            _refreshTimer?.Dispose();
        }
    }
}
