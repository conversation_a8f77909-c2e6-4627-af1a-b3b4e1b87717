using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ZoomableApp.Models;
using ZoomableApp.Services;

namespace ZoomableApp.ViewModels
{
    /// <summary>
    /// ViewModel cho trang báo cáo
    /// </summary>
    public class ReportViewModel : INotifyPropertyChanged
    {
        private readonly ProductionDataService _productionDataService;
        private readonly CsvExportService _csvExportService;
        private ObservableCollection<ProductionData> _reportData;
        private DateTime _fromDate;
        private DateTime _toDate;
        private string _selectedWorkShift;
        private string _selectedReportType;
        private string _statusMessage;
        private bool _isLoading;

        public ReportViewModel(ProductionDataService productionDataService)
        {
            _productionDataService = productionDataService;
            _csvExportService = new CsvExportService();

            InitializeViewModel();

            // Load dữ liệu mặc định (sản lượng ca hiện tại)
            _ = Task.Run(LoadCurrentShiftProductionAsync);
        }

        // Constructor cho trường hợp lỗi config
        public ReportViewModel()
        {
            _productionDataService = null;
            _csvExportService = new CsvExportService();

            InitializeViewModel();

            // Hiển thị lỗi config
            ShowConfigError();
        }

        private void InitializeViewModel()
        {
            // Khởi tạo dữ liệu mặc định
            _reportData = new ObservableCollection<ProductionData>();
            _fromDate = DateTime.Today;
            _toDate = DateTime.Today;
            _selectedWorkShift = "Tất cả";
            _selectedReportType = "Production";
            _statusMessage = "Sẵn sàng";

            // Khởi tạo commands
            ProductionCommand = new RelayCommand(async () => await LoadProductionReportAsync());
            SlowOperationCommand = new RelayCommand(async () => await LoadSlowOperationReportAsync());
            MeasureOperationCommand = new RelayCommand(async () => await LoadMeasureOperationReportAsync());
            MonthlyReportCommand = new RelayCommand(async () => await LoadMonthlyReportAsync());
            ErrorHistoryCommand = new RelayCommand(async () => await LoadErrorHistoryReportAsync());
            SearchCommand = new RelayCommand(async () => await SearchReportDataAsync());
            ExportExcelCommand = new RelayCommand(async () => await ExportToExcelAsync());
        }

        private void ShowConfigError()
        {
            StatusMessage = "Lỗi config file";

            // Thêm một bản ghi lỗi vào DataGrid
            var errorRecord = new ProductionData
            {
                Id = 1,
                Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                Station = "System",
                Product_OK = 0,
                Product_NG = 0,
                Product_Total = 0,
                Time_Complete = 0,
                Time_Delay = 0,
                Time_Stop = 0,
                Number_Stop = 0,
                Error_Code = "CONFIG_ERROR",
                Error_Text = "Lỗi config file - Không thể tải cấu hình PLC",
                WorkShift = "System",
                ReportType = "Error",
                CreatedBy = "System",
                Notes = "Vui lòng kiểm tra file cấu hình PLC"
            };

            ReportData.Add(errorRecord);
        }

        #region Properties

        public ObservableCollection<ProductionData> ReportData
        {
            get => _reportData;
            set
            {
                _reportData = value;
                OnPropertyChanged();
            }
        }

        public DateTime FromDate
        {
            get => _fromDate;
            set
            {
                _fromDate = value;
                OnPropertyChanged();
            }
        }

        public DateTime ToDate
        {
            get => _toDate;
            set
            {
                _toDate = value;
                OnPropertyChanged();
            }
        }

        public string SelectedWorkShift
        {
            get => _selectedWorkShift;
            set
            {
                _selectedWorkShift = value;
                OnPropertyChanged();
            }
        }

        public string SelectedReportType
        {
            get => _selectedReportType;
            set
            {
                _selectedReportType = value;
                OnPropertyChanged();
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        public List<string> WorkShifts { get; } = new List<string>
        {
            "Tất cả",
            "Ca sáng (06:00-11:00)",
            "Ca chiều (14:00-22:00)",
            "Ca hành chính (08:00-17:00)",
            "Ca đêm (22:00-06:00)"
        };

        #endregion

        #region Commands

        public ICommand ProductionCommand { get; private set; }
        public ICommand SlowOperationCommand { get; private set; }
        public ICommand MeasureOperationCommand { get; private set; }
        public ICommand MonthlyReportCommand { get; private set; }
        public ICommand ErrorHistoryCommand { get; private set; }
        public ICommand SearchCommand { get; private set; }
        public ICommand ExportExcelCommand { get; private set; }

        #endregion

        #region Methods

        /// <summary>
        /// Load báo cáo sản lượng
        /// </summary>
        private async Task LoadProductionReportAsync()
        {
            if (_productionDataService == null)
            {
                StatusMessage = "Lỗi config file - Không thể tải dữ liệu";
                return;
            }

            try
            {
                IsLoading = true;
                StatusMessage = "Đang tải báo cáo sản lượng...";
                SelectedReportType = "Production";

                var workShift = SelectedWorkShift == "Tất cả" ? null : SelectedWorkShift;
                var data = await _productionDataService.GetProductionDataAsync(
                    FromDate, ToDate, workShift, "Production");

                ReportData.Clear();
                foreach (var item in data)
                    ReportData.Add(item);

                StatusMessage = $"Đã tải {data.Count} bản ghi sản lượng";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Lỗi: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Load báo cáo thao tác chậm
        /// </summary>
        private async Task LoadSlowOperationReportAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Đang tải báo cáo thao tác chậm...";
                SelectedReportType = "SlowOperation";

                var workShift = SelectedWorkShift == "Tất cả" ? null : SelectedWorkShift;
                var data = await _productionDataService.GetProductionDataAsync(
                    FromDate, ToDate, workShift, "SlowOperation");

                ReportData.Clear();
                foreach (var item in data)
                    ReportData.Add(item);

                StatusMessage = $"Đã tải {data.Count} bản ghi thao tác chậm";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Lỗi: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Load báo cáo đo thao tác
        /// </summary>
        private async Task LoadMeasureOperationReportAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Đang tải báo cáo đo thao tác...";
                SelectedReportType = "MeasureOperation";

                var workShift = SelectedWorkShift == "Tất cả" ? null : SelectedWorkShift;
                var data = await _productionDataService.GetProductionDataAsync(
                    FromDate, ToDate, workShift, "MeasureOperation");

                ReportData.Clear();
                foreach (var item in data)
                    ReportData.Add(item);

                StatusMessage = $"Đã tải {data.Count} bản ghi đo thao tác";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Lỗi: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Load báo cáo tổng hợp tháng
        /// </summary>
        private async Task LoadMonthlyReportAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Đang tải báo cáo tổng hợp tháng...";
                SelectedReportType = "MonthlyReport";

                // Lấy dữ liệu cả tháng
                var startOfMonth = new DateTime(FromDate.Year, FromDate.Month, 1);
                var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

                var data = await _productionDataService.GetProductionDataAsync(
                    startOfMonth, endOfMonth, null, null);

                ReportData.Clear();
                foreach (var item in data)
                    ReportData.Add(item);

                StatusMessage = $"Đã tải {data.Count} bản ghi tháng {FromDate.Month}/{FromDate.Year}";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Lỗi: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Load báo cáo lịch sử lỗi
        /// </summary>
        private async Task LoadErrorHistoryReportAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Đang tải lịch sử lỗi...";
                SelectedReportType = "ErrorHistory";

                var workShift = SelectedWorkShift == "Tất cả" ? null : SelectedWorkShift;
                var data = await _productionDataService.GetProductionDataAsync(
                    FromDate, ToDate, workShift, "ErrorHistory");

                // Lọc chỉ những bản ghi có lỗi
                var errorData = data.Where(d => !string.IsNullOrEmpty(d.Error_Code)).ToList();

                ReportData.Clear();
                foreach (var item in errorData)
                    ReportData.Add(item);

                StatusMessage = $"Đã tải {errorData.Count} bản ghi lỗi";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Lỗi: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Tìm kiếm dữ liệu báo cáo
        /// </summary>
        private async Task SearchReportDataAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Đang tìm kiếm...";

                var workShift = SelectedWorkShift == "Tất cả" ? null : SelectedWorkShift;
                var reportType = SelectedReportType == "Production" ? null : SelectedReportType;
                
                var data = await _productionDataService.GetProductionDataAsync(
                    FromDate, ToDate, workShift, reportType);

                ReportData.Clear();
                foreach (var item in data)
                    ReportData.Add(item);

                StatusMessage = $"Tìm thấy {data.Count} bản ghi";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Lỗi tìm kiếm: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Xuất Excel
        /// </summary>
        private async Task ExportToExcelAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Đang xuất Excel...";

                var data = ReportData.ToList();
                if (!data.Any())
                {
                    StatusMessage = "Không có dữ liệu để xuất";
                    return;
                }

                string filePath = "";
                
                switch (SelectedReportType)
                {
                    case "Production":
                        filePath = _csvExportService.ExportProductionReport(data, FromDate, ToDate);
                        break;
                    case "SlowOperation":
                        filePath = _csvExportService.ExportSlowOperationReport(data);
                        break;
                    case "MeasureOperation":
                        filePath = _csvExportService.ExportMeasureOperationReport(data);
                        break;
                    case "MonthlyReport":
                        filePath = _csvExportService.ExportMonthlyReport(data, FromDate.Month, FromDate.Year);
                        break;
                    case "ErrorHistory":
                        filePath = _csvExportService.ExportErrorHistoryReport(data);
                        break;
                    default:
                        filePath = _csvExportService.ExportProductionReport(data, FromDate, ToDate);
                        break;
                }

                StatusMessage = $"Đã xuất file: {System.IO.Path.GetFileName(filePath)}";
                
                // Mở thư mục chứa file
                System.Diagnostics.Process.Start("explorer.exe", $"/select,\"{filePath}\"");
            }
            catch (Exception ex)
            {
                StatusMessage = $"Lỗi xuất Excel: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Load sản lượng ca hiện tại (mặc định)
        /// </summary>
        private async Task LoadCurrentShiftProductionAsync()
        {
            if (_productionDataService == null)
            {
                // Đã hiển thị lỗi config trong ShowConfigError()
                return;
            }

            try
            {
                var currentShift = WorkShiftHelper.GetShiftName(WorkShiftHelper.GetCurrentShift());
                var data = await _productionDataService.GetProductionDataAsync(
                    DateTime.Today, DateTime.Today, currentShift, "Production");

                ReportData.Clear();
                foreach (var item in data)
                    ReportData.Add(item);

                StatusMessage = $"Sản lượng {currentShift} - {data.Count} bản ghi";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Lỗi tải dữ liệu mặc định: {ex.Message}";
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }


}
