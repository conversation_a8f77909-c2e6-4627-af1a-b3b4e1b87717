using Microsoft.Data.Sqlite;
using System.IO;
using ZoomableApp.Models;

namespace ZoomableApp.Services
{
    public class UserService
    {
        private readonly string _connectionString;
        private readonly ILoggerService? _logger;

        public UserService()
        {
            var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "panaDB.db");
            _connectionString = $"Data Source={dbPath}";

            // Kiểm tra xem ServiceContainer đã được khởi tạo chưa
            if (ServiceContainer.IsRegistered<ILoggerService>())
            {
                _logger = ServiceContainer.GetService<ILoggerService>();
            }
        }

        public async Task<User?> AuthenticateUserAsync(string username, string password)
        {
            try
            {
                if (_logger != null)
                {
                    _logger.LogInfo($"UserService: Attempting to authenticate user '{username}'");
                    _logger.LogDebug($"UserService: Connection string: {_connectionString}");
                }

                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                if (_logger != null)
                {
                    _logger.LogDebug("UserService: Database connection opened successfully");
                }

                // First, let's check what users exist in the database
                var checkCommand = connection.CreateCommand();
                checkCommand.CommandText = "SELECT username, password FROM users";
                using var checkReader = await checkCommand.ExecuteReaderAsync();
                if (_logger != null)
                {
                    _logger.LogDebug("UserService: Existing users in database:");
                    while (await checkReader.ReadAsync())
                    {
                        var dbUsername = checkReader.IsDBNull(0) ? "NULL" : checkReader.GetString(0);
                        var dbPassword = checkReader.IsDBNull(1) ? "NULL" : checkReader.GetString(1);
                        _logger.LogDebug($"  - Username: '{dbUsername}', Password: '{dbPassword}'");
                    }
                }
                checkReader.Close();

                var command = connection.CreateCommand();
                command.CommandText = @"
                    SELECT id, username, password, role, fullname, rfid
                    FROM users
                    WHERE username = @username AND password = @password";

                command.Parameters.AddWithValue("@username", username);
                command.Parameters.AddWithValue("@password", password);

                if (_logger != null)
                {
                    _logger.LogDebug($"UserService: Executing query with username='{username}' and password='{password}'");
                }

                using var reader = await command.ExecuteReaderAsync();

                if (await reader.ReadAsync())
                {
                    if (_logger != null)
                    {
                        _logger.LogInfo("UserService: User found and authenticated successfully");
                    }
                    return new User
                    {
                        Id = reader.GetInt32(0),
                        Username = reader.IsDBNull(1) ? "" : reader.GetString(1),
                        Password = reader.IsDBNull(2) ? "" : reader.GetString(2),
                        Role = reader.IsDBNull(3) ? "" : reader.GetString(3),
                        Fullname = reader.IsDBNull(4) ? "" : reader.GetString(4),
                        Rfid = reader.IsDBNull(5) ? "" : reader.GetString(5)
                    };
                }

                if (_logger != null)
                {
                    _logger.LogWarning("UserService: No matching user found");
                }
                return null;
            }
            catch (Exception ex)
            {
                if (_logger != null)
                {
                    _logger.LogError($"Error authenticating user: {ex.Message}", ex);
                }
                return null;
            }
        }

        public async Task<User?> AuthenticateRfidAsync(string rfidCode)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                if (_logger != null)
                {
                    _logger.LogInfo($"UserService: Authenticating RFID code: '{rfidCode}'");
                }

                var command = connection.CreateCommand();
                command.CommandText = @"
                    SELECT id, username, password, role, fullname, rfid
                    FROM users
                    WHERE rfid = @rfidCode";

                command.Parameters.AddWithValue("@rfidCode", rfidCode.ToUpper());

                using var reader = await command.ExecuteReaderAsync();

                if (await reader.ReadAsync())
                {
                    if (_logger != null)
                    {
                        _logger.LogInfo("UserService: RFID authentication successful");
                    }
                    return new User
                    {
                        Id = reader.GetInt32(0),
                        Username = reader.IsDBNull(1) ? "" : reader.GetString(1),
                        Password = reader.IsDBNull(2) ? "" : reader.GetString(2),
                        Role = reader.IsDBNull(3) ? "" : reader.GetString(3),
                        Fullname = reader.IsDBNull(4) ? "" : reader.GetString(4),
                        Rfid = reader.IsDBNull(5) ? "" : reader.GetString(5)
                    };
                }

                if (_logger != null)
                {
                    _logger.LogWarning("UserService: No matching RFID found");
                }
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error authenticating RFID: {ex.Message}");
                return null;
            }
        }

        public async Task<List<User>> GetAllUsersAsync()
        {
            var users = new List<User>();
            
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var command = connection.CreateCommand();
                command.CommandText = "SELECT id, username, password, role, fullname, rfid FROM users";

                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    users.Add(new User
                    {
                        Id = reader.GetInt32(0),
                        Username = reader.IsDBNull(1) ? "" : reader.GetString(1),
                        Password = reader.IsDBNull(2) ? "" : reader.GetString(2),
                        Role = reader.IsDBNull(3) ? "" : reader.GetString(3),
                        Fullname = reader.IsDBNull(4) ? "" : reader.GetString(4),
                        Rfid = reader.IsDBNull(5) ? "" : reader.GetString(5)
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting users: {ex.Message}");
            }

            return users;
        }

        public async Task<bool> CreateUserAsync(User user)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var command = connection.CreateCommand();
                command.CommandText = @"
                    INSERT INTO users (username, password, role, fullname, rfid)
                    VALUES (@username, @password, @role, @fullname, @rfid)";

                command.Parameters.AddWithValue("@username", user.Username);
                command.Parameters.AddWithValue("@password", user.Password);
                command.Parameters.AddWithValue("@role", user.Role);
                command.Parameters.AddWithValue("@fullname", user.Fullname);
                command.Parameters.AddWithValue("@rfid", user.Rfid ?? "");

                var result = await command.ExecuteNonQueryAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating user: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> UpdateUserAsync(User user)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var command = connection.CreateCommand();
                command.CommandText = @"
                    UPDATE users
                    SET username = @username, password = @password, role = @role,
                        fullname = @fullname, rfid = @rfid
                    WHERE id = @id";

                command.Parameters.AddWithValue("@id", user.Id);
                command.Parameters.AddWithValue("@username", user.Username);
                command.Parameters.AddWithValue("@password", user.Password);
                command.Parameters.AddWithValue("@role", user.Role);
                command.Parameters.AddWithValue("@fullname", user.Fullname);
                command.Parameters.AddWithValue("@rfid", user.Rfid ?? "");

                var result = await command.ExecuteNonQueryAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating user: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeleteUserAsync(int userId)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var command = connection.CreateCommand();
                command.CommandText = "DELETE FROM users WHERE id = @id";
                command.Parameters.AddWithValue("@id", userId);

                var result = await command.ExecuteNonQueryAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting user: {ex.Message}");
                return false;
            }
        }

        public async Task<User?> GetUserByIdAsync(int userId)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var command = connection.CreateCommand();
                command.CommandText = @"
                    SELECT id, username, password, role, fullname, rfid
                    FROM users
                    WHERE id = @id";

                command.Parameters.AddWithValue("@id", userId);

                using var reader = await command.ExecuteReaderAsync();

                if (await reader.ReadAsync())
                {
                    return new User
                    {
                        Id = reader.GetInt32(0),
                        Username = reader.IsDBNull(1) ? "" : reader.GetString(1),
                        Password = reader.IsDBNull(2) ? "" : reader.GetString(2),
                        Role = reader.IsDBNull(3) ? "" : reader.GetString(3),
                        Fullname = reader.IsDBNull(4) ? "" : reader.GetString(4),
                        Rfid = reader.IsDBNull(5) ? "" : reader.GetString(5)
                    };
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting user by ID: {ex.Message}");
                return null;
            }
        }

        public async Task<bool> IsUsernameExistsAsync(string username, int excludeUserId = 0)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var command = connection.CreateCommand();
                command.CommandText = @"
                    SELECT COUNT(*) FROM users
                    WHERE username = @username AND id != @excludeUserId";

                command.Parameters.AddWithValue("@username", username);
                command.Parameters.AddWithValue("@excludeUserId", excludeUserId);

                var result = await command.ExecuteScalarAsync();
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking username exists: {ex.Message}");
                return false;
            }
        }
    }
}
