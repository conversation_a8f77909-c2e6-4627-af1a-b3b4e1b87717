{"ExcelSettings": {"PlanFilePath": "C:\\Project-Dat\\PANA\\weeklyPlans.xlsx", "MaintenancePlanPath": "C:\\Project-Dat\\PANA\\maintenancePlans.xlsx", "IdlePlansFilePath": "C:\\Project-Dat\\PANA\\idleplans.xlsx"}, "PlcSettings": {"Mode": "Real", "Description": "PLC operating mode: 'Real' for production, 'Mock' for testing/demo", "MockDataUpdateInterval": 10000, "EnableRandomVariation": true, "AutoConnectOnStartup": true}, "DashboardSettings": {"RefreshInterval": 5000, "EnableRealTimeUpdates": true, "ChartAnimationDuration": 500}, "LayoutSettings": {"DefaultLayout": "Mainline", "Description": "Layout configuration: 'Mainline' for production line, 'Inspection' for inspection line"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}}