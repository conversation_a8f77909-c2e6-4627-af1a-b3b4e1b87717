using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using ZoomableApp.Models;
using ZoomableApp.Services;

namespace ZoomableApp.ViewModels
{
    public class UsersViewModel : INotifyPropertyChanged
    {
        private readonly UserService _userService;
        private ObservableCollection<User> _users;
        private User? _selectedUser;
        private bool _isLoading;
        private string _statusMessage = "";
        private bool _isAddUserDialogOpen;
        private bool _isEditUserDialogOpen;
        private User _newUser;
        private User _editingUser;
        private string _searchText = "";

        public UsersViewModel()
        {
            _userService = new UserService();
            _users = new ObservableCollection<User>();
            _newUser = new User();
            _editingUser = new User();

            // Commands
            LoadUsersCommand = new RelayCommand(async () => await LoadUsersAsync());
            AddUserCommand = new RelayCommand(() => OpenAddUserDialog(), CanAddUser);
            EditUserCommand = new RelayCommand(() => OpenEditUserDialog(), CanEditUser);
            DeleteUserCommand = new RelayCommand(async () => await DeleteUserAsync(), CanDeleteUser);
            SaveNewUserCommand = new RelayCommand(async () => await SaveNewUserAsync(), CanSaveNewUser);
            SaveEditUserCommand = new RelayCommand(async () => await SaveEditUserAsync(), CanSaveEditUser);
            CancelAddUserCommand = new RelayCommand(() => CloseAddUserDialog());
            CancelEditUserCommand = new RelayCommand(() => CloseEditUserDialog());
            SearchCommand = new RelayCommand(async () => await SearchUsersAsync());
            RefreshCommand = new RelayCommand(async () => await LoadUsersAsync());

            // Load initial data
            _ = Task.Run(async () => await LoadUsersAsync());
        }

        #region Properties

        public ObservableCollection<User> Users
        {
            get => _users;
            set
            {
                _users = value;
                OnPropertyChanged();
            }
        }

        public User? SelectedUser
        {
            get => _selectedUser;
            set
            {
                _selectedUser = value;
                OnPropertyChanged();
                CommandManager.InvalidateRequerySuggested();
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        public bool IsAddUserDialogOpen
        {
            get => _isAddUserDialogOpen;
            set
            {
                _isAddUserDialogOpen = value;
                OnPropertyChanged();
            }
        }

        public bool IsEditUserDialogOpen
        {
            get => _isEditUserDialogOpen;
            set
            {
                _isEditUserDialogOpen = value;
                OnPropertyChanged();
            }
        }

        public User NewUser
        {
            get => _newUser;
            set
            {
                _newUser = value;
                OnPropertyChanged();
            }
        }

        public User EditingUser
        {
            get => _editingUser;
            set
            {
                _editingUser = value;
                OnPropertyChanged();
            }
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                _searchText = value;
                OnPropertyChanged();
            }
        }

        // Available roles
        public ObservableCollection<string> AvailableRoles { get; } = new ObservableCollection<string>
        {
            "Admin",
            "Manager", 
            "Operator",
            "User"
        };

        // Current user info for permission checks
        public bool IsCurrentUserAdmin => UserSession.CurrentUser?.Role == "Admin";
        public bool CanManageAdminUsers => IsCurrentUserAdmin;

        #endregion

        #region Commands

        public ICommand LoadUsersCommand { get; }
        public ICommand AddUserCommand { get; }
        public ICommand EditUserCommand { get; }
        public ICommand DeleteUserCommand { get; }
        public ICommand SaveNewUserCommand { get; }
        public ICommand SaveEditUserCommand { get; }
        public ICommand CancelAddUserCommand { get; }
        public ICommand CancelEditUserCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand RefreshCommand { get; }

        #endregion

        #region Methods

        private async Task LoadUsersAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Đang tải danh sách người dùng...";

                var users = await _userService.GetAllUsersAsync();
                
                Application.Current.Dispatcher.Invoke(() =>
                {
                    Users.Clear();
                    foreach (var user in users)
                    {
                        Users.Add(user);
                    }
                });

                StatusMessage = $"Đã tải {users.Count} người dùng";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Lỗi tải dữ liệu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void OpenAddUserDialog()
        {
            NewUser = new User
            {
                Role = "User" // Default role
            };
            IsAddUserDialogOpen = true;
        }

        private void CloseAddUserDialog()
        {
            IsAddUserDialogOpen = false;
            NewUser = new User();
        }

        private void OpenEditUserDialog()
        {
            if (SelectedUser != null)
            {
                EditingUser = new User
                {
                    Id = SelectedUser.Id,
                    Username = SelectedUser.Username,
                    Password = SelectedUser.Password,
                    Role = SelectedUser.Role,
                    Fullname = SelectedUser.Fullname,
                    Rfid = SelectedUser.Rfid
                };
                IsEditUserDialogOpen = true;
            }
        }

        private void CloseEditUserDialog()
        {
            IsEditUserDialogOpen = false;
            EditingUser = new User();
        }

        private async Task SaveNewUserAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Đang thêm người dùng...";

                // Validation
                if (string.IsNullOrWhiteSpace(NewUser.Username))
                {
                    StatusMessage = "Tên đăng nhập không được để trống";
                    return;
                }

                if (string.IsNullOrWhiteSpace(NewUser.Password))
                {
                    StatusMessage = "Mật khẩu không được để trống";
                    return;
                }

                if (string.IsNullOrWhiteSpace(NewUser.Fullname))
                {
                    StatusMessage = "Họ tên không được để trống";
                    return;
                }

                // Check if username already exists
                if (Users.Any(u => u.Username.Equals(NewUser.Username, StringComparison.OrdinalIgnoreCase)))
                {
                    StatusMessage = "Tên đăng nhập đã tồn tại";
                    return;
                }

                // Permission check for Admin role
                if (NewUser.Role == "Admin" && !IsCurrentUserAdmin)
                {
                    StatusMessage = "Chỉ Admin mới có quyền tạo tài khoản Admin";
                    return;
                }

                var success = await _userService.CreateUserAsync(NewUser);
                
                if (success)
                {
                    StatusMessage = "Thêm người dùng thành công";
                    CloseAddUserDialog();
                    await LoadUsersAsync();
                }
                else
                {
                    StatusMessage = "Lỗi thêm người dùng";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Lỗi: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task SaveEditUserAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Đang cập nhật người dùng...";

                // Validation
                if (string.IsNullOrWhiteSpace(EditingUser.Username))
                {
                    StatusMessage = "Tên đăng nhập không được để trống";
                    return;
                }

                if (string.IsNullOrWhiteSpace(EditingUser.Fullname))
                {
                    StatusMessage = "Họ tên không được để trống";
                    return;
                }

                // Check if username already exists (excluding current user)
                if (Users.Any(u => u.Id != EditingUser.Id && u.Username.Equals(EditingUser.Username, StringComparison.OrdinalIgnoreCase)))
                {
                    StatusMessage = "Tên đăng nhập đã tồn tại";
                    return;
                }

                // Permission check for Admin role
                if (EditingUser.Role == "Admin" && !IsCurrentUserAdmin)
                {
                    StatusMessage = "Chỉ Admin mới có quyền phân quyền Admin";
                    return;
                }

                // Prevent removing Admin role from the last Admin
                if (SelectedUser?.Role == "Admin" && EditingUser.Role != "Admin")
                {
                    var adminCount = Users.Count(u => u.Role == "Admin");
                    if (adminCount <= 1)
                    {
                        StatusMessage = "Không thể xóa quyền Admin của người dùng cuối cùng";
                        return;
                    }
                }

                var success = await _userService.UpdateUserAsync(EditingUser);
                
                if (success)
                {
                    StatusMessage = "Cập nhật người dùng thành công";
                    CloseEditUserDialog();
                    await LoadUsersAsync();
                }
                else
                {
                    StatusMessage = "Lỗi cập nhật người dùng";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Lỗi: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task DeleteUserAsync()
        {
            if (SelectedUser == null) return;

            try
            {
                // Permission checks
                if (SelectedUser.Id == UserSession.CurrentUser?.Id)
                {
                    StatusMessage = "Không thể xóa tài khoản của chính mình";
                    return;
                }

                if (SelectedUser.Role == "Admin")
                {
                    if (!IsCurrentUserAdmin)
                    {
                        StatusMessage = "Chỉ Admin mới có quyền xóa tài khoản Admin";
                        return;
                    }

                    var adminCount = Users.Count(u => u.Role == "Admin");
                    if (adminCount <= 1)
                    {
                        StatusMessage = "Không thể xóa tài khoản Admin cuối cùng";
                        return;
                    }
                }

                var result = MessageBox.Show(
                    $"Bạn có chắc chắn muốn xóa người dùng '{SelectedUser.Fullname}'?",
                    "Xác nhận xóa",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    IsLoading = true;
                    StatusMessage = "Đang xóa người dùng...";

                    var success = await _userService.DeleteUserAsync(SelectedUser.Id);
                    
                    if (success)
                    {
                        StatusMessage = "Xóa người dùng thành công";
                        await LoadUsersAsync();
                        SelectedUser = null;
                    }
                    else
                    {
                        StatusMessage = "Lỗi xóa người dùng";
                    }
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Lỗi: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task SearchUsersAsync()
        {
            if (string.IsNullOrWhiteSpace(SearchText))
            {
                await LoadUsersAsync();
                return;
            }

            try
            {
                IsLoading = true;
                StatusMessage = "Đang tìm kiếm...";

                var allUsers = await _userService.GetAllUsersAsync();
                var filteredUsers = allUsers.Where(u => 
                    u.Username.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    u.Fullname.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    u.Role.Contains(SearchText, StringComparison.OrdinalIgnoreCase)
                ).ToList();

                Application.Current.Dispatcher.Invoke(() =>
                {
                    Users.Clear();
                    foreach (var user in filteredUsers)
                    {
                        Users.Add(user);
                    }
                });

                StatusMessage = $"Tìm thấy {filteredUsers.Count} người dùng";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Lỗi tìm kiếm: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region Command CanExecute Methods

        private bool CanAddUser()
        {
            return !IsLoading;
        }

        private bool CanEditUser()
        {
            return !IsLoading && SelectedUser != null;
        }

        private bool CanDeleteUser()
        {
            return !IsLoading && SelectedUser != null && SelectedUser.Id != UserSession.CurrentUser?.Id;
        }

        private bool CanSaveNewUser()
        {
            return !IsLoading && 
                   !string.IsNullOrWhiteSpace(NewUser?.Username) && 
                   !string.IsNullOrWhiteSpace(NewUser?.Password) &&
                   !string.IsNullOrWhiteSpace(NewUser?.Fullname);
        }

        private bool CanSaveEditUser()
        {
            return !IsLoading && 
                   !string.IsNullOrWhiteSpace(EditingUser?.Username) && 
                   !string.IsNullOrWhiteSpace(EditingUser?.Fullname);
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
