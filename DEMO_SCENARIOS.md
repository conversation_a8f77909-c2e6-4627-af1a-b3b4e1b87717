# 🎬 DEMO SCENARIOS CHO MOCK DATA

## 🎯 **HƯỚNG DẪN DEMO STOP TIMES CHART**

### **📋 CHUẨN BỊ DEMO:**

#### **1. Ki<PERSON>m tra Configuration:**
```json
// Trong appsettings.json
{
  "PlcSettings": {
    "Mode": "Mock",                    // ✅ Bắt buộc cho demo
    "EnableRandomVariation": true,     // ✅ Cho dynamic demo
    "MockDataUpdateInterval": 2000     // ✅ Update mỗi 2 giây
  },
  "DashboardSettings": {
    "RefreshInterval": 5000            // ✅ Refresh chart mỗi 5 giây
  }
}
```

#### **2. Khởi động Application:**
```bash
cd ZoomableApp
dotnet run
```

#### **3. Navigate to Homepage:**
- Mở ứng dụng
- Click vào tab "Trang chủ" 
- Tìm Stop Times Chart (góc dưới bên trái)

---

## 🎭 **DEMO SCENARIOS**

### **SCENARIO 1: STATIC DEMO (Consistent Data)**

#### **Setup:**
```json
"EnableRandomVariation": false
```

#### **Expected Results:**
- **ST1**: 12 stops (highest)
- **ST2**: 10 stops  
- **ST3**: 8 stops
- **ST4**: 6 stops
- **ST5**: 4 stops (lowest)

#### **Demo Points:**
✅ "Đây là dữ liệu consistent cho presentation"  
✅ "ST1 và ST2 là các trạm có vấn đề nhiều nhất"  
✅ "Chart tự động sort theo số lần dừng"  
✅ "UI hiển thị tổng số lần dừng: 40 lần"  

---

### **SCENARIO 2: DYNAMIC DEMO (Random Variation)**

#### **Setup:**
```json
"EnableRandomVariation": true
```

#### **Expected Results:**
- Data thay đổi mỗi 5 giây
- **Problematic stations** (ST1-ST2): 8-15 stops
- **Medium stations** (ST3-ST4): 3-8 stops  
- **Good stations** (ST5+): 0-3 stops

#### **Demo Points:**
✅ "Dữ liệu thay đổi real-time như PLC thật"  
✅ "Một số trạm luôn có vấn đề nhiều hơn (realistic)"  
✅ "Chart auto-refresh, không cần manual reload"  
✅ "Timestamp cập nhật liên tục"  

---

### **SCENARIO 3: PRODUCTION SIMULATION**

#### **Demo Flow:**
1. **Start với Mock Mode:**
   - Hiển thị mock data ngay lập tức
   - Charts load nhanh, không wait

2. **Explain Real Mode:**
   - "Trong production, chỉ cần đổi Mode: 'Real'"
   - "Không thay đổi UI code gì cả"
   - "Same charts, real PLC data"

3. **Show Configuration:**
   - Mở appsettings.json
   - Highlight sự đơn giản của việc switch modes

#### **Demo Points:**
✅ "Zero downtime switching giữa modes"  
✅ "Development team có thể work mà không cần PLC"  
✅ "Production ready với minimal configuration"  

---

## 🎨 **UI FEATURES DEMO**

### **Visual Elements:**
- **🔴 Red Icon**: Indicates stop times (problems)
- **📊 Column Chart**: Easy to compare stations
- **📈 Summary Box**: Total stop count highlighted
- **⏰ Timestamp**: Shows last update time
- **🏷️ Footer**: Explains "Top 5 stations"

### **Interactive Features:**
- **Auto-refresh**: Watch numbers change
- **Responsive**: Resize window to test
- **Professional**: Modern dark theme

---

## 🔧 **TROUBLESHOOTING DEMO**

### **Nếu Chart không hiển thị:**

#### **1. Kiểm tra Mode:**
```bash
# Trong Debug Output
"ConfigurationService: PLC Mode = Mock"
"StopTimesChart: Generated mock data for 5 stations"
```

#### **2. Kiểm tra ServiceContainer:**
```bash
# Trong Debug Output  
"ServiceContainer: Registered singleton MockDashboardDataService"
"ServiceContainer: Initialization completed successfully"
```

#### **3. Force Refresh:**
- Restart application
- Check appsettings.json syntax
- Verify MockDataUpdateInterval > 0

---

## 📊 **ADVANCED DEMO SCENARIOS**

### **SCENARIO 4: MAINTENANCE IMPACT**

#### **Story:**
"Hôm nay ST1 đang maintenance, nên stop count thấp hơn bình thường"

#### **Demo:**
- Point out khi ST1 có số thấp bất thường
- Explain realistic variation patterns
- Show how data reflects real operations

### **SCENARIO 5: SHIFT COMPARISON**

#### **Story:**
"Ca đêm thường có nhiều stop hơn do ít operator"

#### **Demo:**
- Explain time-based patterns
- Show how mock data simulates real scenarios
- Discuss shift-based analysis potential

### **SCENARIO 6: QUALITY CORRELATION**

#### **Story:**
"Stations có nhiều stop thường có quality issues"

#### **Demo:**
- Connect stop times với quality data
- Show integrated dashboard approach
- Explain root cause analysis capabilities

---

## 🎯 **DEMO SCRIPT TEMPLATE**

### **Opening (30 seconds):**
> "Đây là Stop Times Chart hiển thị top 5 trạm có nhiều lần dừng nhất. 
> Hiện tại đang chạy Mock mode để demo, nhưng trong production sẽ 
> connect trực tiếp với PLC."

### **Features (60 seconds):**
> "Chart tự động refresh mỗi 5 giây. Bạn thấy ST1 và ST2 luôn có 
> vấn đề nhiều nhất - đây là pattern realistic. Tổng số lần dừng 
> hiển thị ở đây, và timestamp cho biết lần cập nhật cuối."

### **Technical (30 seconds):**
> "Để switch sang real PLC, chỉ cần đổi config từ Mock sang Real. 
> Không cần thay đổi code gì. UI sẽ tự động nhận dữ liệu từ PLC thật."

### **Closing (30 seconds):**
> "Mock data cho phép development team làm việc độc lập, test UI, 
> và demo cho stakeholders mà không cần PLC hardware."

---

## 🚀 **NEXT DEMO EXTENSIONS**

### **Ready to Add:**
- Quality Charts mock data
- Plan vs Actual Charts  
- Idle Hours Charts
- Production Trends
- Error History

### **Advanced Features:**
- Historical data simulation
- Alarm/alert scenarios  
- Equipment breakdown simulation
- Maintenance schedule impact

---

**🎬 DEMO READY! Chỉ cần chạy `dotnet run` và navigate đến homepage.**

---

## 🆕 **DEMO GUIDE CHO TẤT CẢ 5 CHARTS**

### **📍 VỊ TRÍ CÁC CHARTS TRÊN HOMEPAGE:**

```
┌─────────────────────────────────────────────────────────────┐
│                    HOMEPAGE LAYOUT                         │
├─────────────────────┬─────────────────────┬─────────────────┤
│                     │                     │                 │
│   Panel 3 - Left    │   Panel 3 - Right   │                 │
│  Daily Idle Hours   │ Monthly Idle Hours  │                 │
│   (Half Pie)        │   (Half Pie)        │                 │
│                     │                     │                 │
├─────────────────────┼─────────────────────┤   Stop Times    │
│                     │                     │     Chart       │
│   Panel 4 - Left    │   Panel 4 - Right   │   (Column)      │
│ Shift Plan vs Actual│  Shift Quality      │                 │
│   (Half Pie)        │   (Full Pie)        │                 │
│                     │                     │                 │
└─────────────────────┴─────────────────────┴─────────────────┘
```

### **🎭 COMPREHENSIVE DEMO SCRIPT:**

#### **Opening (60 seconds):**
> "Đây là dashboard tổng quan với 5 charts real-time. Tất cả đang chạy
> Mock mode để demo, nhưng trong production sẽ connect trực tiếp với PLC.
> Mỗi chart tự động refresh mỗi 5 giây với dữ liệu realistic."

#### **Chart-by-Chart Demo (3 phút):**

**1. Daily Idle Hours (Panel 3 - Left):**
> "Chart này hiển thị thời gian nghỉ hàng ngày. Xanh là thời gian hoạt động,
> đỏ là thời gian nghỉ. Hiệu suất hiện tại là 86.7% - rất tốt cho sản xuất."

**2. Monthly Idle Hours (Panel 3 - Right):**
> "Tương tự nhưng theo tháng. Màu xanh dương và cam. Giúp so sánh
> performance dài hạn và lập kế hoạch bảo dưỡng."

**3. Shift Plan vs Actual (Panel 4 - Left):**
> "Ca hiện tại đang đạt X% kế hoạch. Nếu vượt kế hoạch sẽ hiển thị màu
> vàng cho phần vượt. Gap âm nghĩa là chưa đạt target."

**4. Shift Quality (Panel 4 - Right):**
> "Chất lượng sản phẩm ca hiện tại. Xanh là OK, đỏ là NG, cam là Rework.
> Quality rate hiện tại X% - đạt tiêu chuẩn sản xuất."

**5. Stop Times (Bottom):**
> "Top 5 trạm có nhiều lần dừng nhất. ST1, ST2 thường có vấn đề nhiều hơn.
> Giúp identify bottlenecks và prioritize maintenance."

#### **Technical Features (60 seconds):**
> "Tất cả charts đều auto-refresh synchronized. Data realistic với random
> variation. Để switch sang real PLC, chỉ cần đổi config Mode từ Mock sang Real.
> UI sẽ tự động adapt mà không cần restart."

#### **Business Value (30 seconds):**
> "Dashboard này cung cấp 360° view của production: efficiency, performance,
> quality, và reliability. Managers có thể quick decision making dựa trên
> real-time data visualization."

### **🎯 DEMO SCENARIOS CHO TỪNG CHART:**

#### **SCENARIO A: NORMAL OPERATIONS**
```json
"EnableRandomVariation": false
```
- Daily Idle: 86.7% utilization (consistent)
- Monthly Idle: 85% utilization (stable)
- Shift Plan: 92% achievement (slightly behind)
- Shift Quality: 95% OK rate (excellent)
- Stop Times: Predictable pattern

#### **SCENARIO B: DYNAMIC OPERATIONS**
```json
"EnableRandomVariation": true
```
- All charts change every 5 seconds
- Realistic variations trong acceptable ranges
- Demonstrates real-time monitoring capabilities

#### **SCENARIO C: PROBLEM IDENTIFICATION**
- Point out khi Stop Times chart shows high values
- Correlate với Quality chart showing more NG
- Explain how Idle Hours might increase due to stops

### **🔧 INTERACTIVE DEMO FEATURES:**

#### **Real-time Watching:**
1. Start demo với static data
2. Enable random variation
3. Watch all charts update simultaneously
4. Point out timestamp changes

#### **Configuration Demo:**
1. Show appsettings.json
2. Change RefreshInterval từ 5000 to 2000
3. Restart app to show faster updates
4. Explain production vs demo settings

#### **Mode Switching Demo:**
1. Show current Mock mode
2. Explain Real mode configuration
3. Demonstrate zero code changes needed
4. Show fallback behavior

### **📊 METRICS TO HIGHLIGHT:**

#### **Efficiency Metrics:**
- Daily Utilization: 75-95%
- Monthly Utilization: 80-90%
- Idle Hours: 3-6 hours daily

#### **Performance Metrics:**
- Plan Achievement: 85-115%
- Gap Analysis: ±20 units
- Shift Progress: Real-time tracking

#### **Quality Metrics:**
- OK Rate: 90-98%
- NG Rate: 0-5%
- Rework Rate: 0-3%

#### **Reliability Metrics:**
- Stop Count: 0-15 per station
- Top Problematic Stations: ST1, ST2
- Maintenance Priorities: Data-driven

### **🎬 ADVANCED DEMO TECHNIQUES:**

#### **Storytelling Approach:**
1. **Morning Briefing**: "Let's check yesterday's performance..."
2. **Shift Handover**: "Current shift is performing at..."
3. **Problem Solving**: "ST1 has high stops, let's investigate..."
4. **Planning**: "Based on trends, we should..."

#### **Comparative Analysis:**
- Compare daily vs monthly idle patterns
- Correlate stop times với quality issues
- Show plan vs actual trends
- Identify improvement opportunities

#### **Future Roadmap:**
- Historical trending capabilities
- Predictive analytics integration
- Mobile dashboard access
- Alert/notification system

---

**🎊 COMPLETE DASHBOARD DEMO READY!**
- ✅ **5 Charts**: All functional với realistic mock data
- ✅ **Professional UI**: Modern design với proper styling
- ✅ **Real-time**: Synchronized updates every 5 seconds
- ✅ **Business Ready**: Production-quality visualization
- ✅ **Demo Scripts**: Comprehensive scenarios available

**🚀 PERFECT FOR STAKEHOLDER PRESENTATIONS!**
