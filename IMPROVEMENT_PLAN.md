# 🚀 KẾ HOẠCH CẢI TIẾN PLC & DASHBOARD ARCHITECTURE

## 📊 HIỆN TRẠNG ĐÁNH GIÁ

### ✅ ĐIỂM MẠNH ĐÃ CÓ:
- **Interface chuẩn**: IPlcService đã trừu tượng hóa tốt
- **Config linh hoạt**: JSON configs cho PLC connections và register mapping  
- **Service layer**: ProductionDataService, DashboardDataService đã có
- **Dashboard**: 6 charts với real-time data binding
- **Enum system**: PlcDeviceAddress đầy đủ và có tổ chức

### ⚠️ ĐIỂM CẦN CẢI THIỆN:
- **Thiếu Mock Service**: Không có fake PLC service cho test/demo
- **Hard Dependencies**: Dashboard phụ thuộc trực tiếp vào real PLC service
- **Không có DI**: Khó chuyển đổi giữa real/mock mode
- **Test khó khăn**: Cần PLC thật để test dashboard

## 🎯 KỊCH BẢN CẢI TIẾN

### BƯỚC 1: TẠO MOCK PLC SERVICE
```csharp
// Services/MockPlcService.cs
public class MockPlcService : IPlcService
{
    private readonly Random _random = new Random();
    private readonly Dictionary<PlcDeviceAddress, object> _mockData;
    
    public bool IsConnected { get; private set; } = false;
    
    public Task<PlcReadResult> ReadAsync(PlcDeviceAddress deviceAddress)
    {
        // Generate realistic mock data based on device type
        var value = GenerateMockValue(deviceAddress);
        return Task.FromResult(PlcReadResult.Success(value));
    }
    
    private object GenerateMockValue(PlcDeviceAddress address)
    {
        return address switch
        {
            PlcDeviceAddress.Product_OK => _random.Next(80, 120),
            PlcDeviceAddress.Product_NG => _random.Next(5, 15),
            PlcDeviceAddress.SystemReadyBit => true,
            _ => _random.Next(0, 100)
        };
    }
}
```

### BƯỚC 2: THÊM SERVICE MODE CONFIG
```json
// appsettings.json
{
  "PlcSettings": {
    "Mode": "Mock", // "Real" | "Mock"
    "MockDataUpdateInterval": 2000,
    "EnableRandomVariation": true
  }
}
```

### BƯỚC 3: TẠO SERVICE FACTORY
```csharp
// Services/PlcServiceFactory.cs
public static class PlcServiceFactory
{
    public static IPlcService CreatePlcService(string plcId, PlcMode mode)
    {
        return mode switch
        {
            PlcMode.Mock => new MockPlcService(plcId),
            PlcMode.Real => new MitsubishiPlcService(plcId),
            _ => throw new ArgumentException($"Unsupported PLC mode: {mode}")
        };
    }
}
```

### BƯỚC 4: REFACTOR DASHBOARD SERVICE
```csharp
// Services/DashboardDataService.cs - Updated
public class DashboardDataService
{
    private readonly IPlcService _plcService;
    
    public DashboardDataService(IPlcService plcService)
    {
        _plcService = plcService; // Inject interface, not concrete class
    }
    
    public async Task<ProductionSummaryData> GetProductionDataAsync()
    {
        // Use injected service instead of direct PLC calls
        var okResult = await _plcService.ReadAsync(PlcDeviceAddress.Product_OK);
        var ngResult = await _plcService.ReadAsync(PlcDeviceAddress.Product_NG);
        
        return new ProductionSummaryData
        {
            OkQuantity = okResult.IsSuccess ? Convert.ToInt32(okResult.Value) : 0,
            NgQuantity = ngResult.IsSuccess ? Convert.ToInt32(ngResult.Value) : 0
        };
    }
}
```

### BƯỚC 5: DEPENDENCY INJECTION SETUP
```csharp
// Services/ServiceContainer.cs
public static class ServiceContainer
{
    private static readonly Dictionary<Type, object> _services = new();
    
    public static void RegisterServices(PlcMode mode)
    {
        // Register PLC services based on mode
        foreach (var plcConfig in ConfigLoader.LoadPlcConfigs())
        {
            var plcService = PlcServiceFactory.CreatePlcService(plcConfig.Id, mode);
            _services[typeof(IPlcService)] = plcService;
        }
        
        // Register dashboard service with PLC dependency
        var dashboardService = new DashboardDataService(_services[typeof(IPlcService)] as IPlcService);
        _services[typeof(DashboardDataService)] = dashboardService;
    }
    
    public static T GetService<T>() => (T)_services[typeof(T)];
}
```

### BƯỚC 6: UPDATE MAINWINDOW
```csharp
// MainWindow.xaml.cs - Updated initialization
private async Task InitializeLazyServices()
{
    // Read mode from config
    var mode = ConfigurationManager.AppSettings["PlcMode"] == "Mock" 
        ? PlcMode.Mock 
        : PlcMode.Real;
    
    // Setup DI container
    ServiceContainer.RegisterServices(mode);
    
    // Get services from container
    _dashboardDataService = ServiceContainer.GetService<DashboardDataService>();
    
    // Rest of initialization...
}
```

## 🧪 TESTING SCENARIOS

### Mock Mode Benefits:
1. **Demo without PLC**: Dashboard works with realistic fake data
2. **Development**: No need for physical PLC connection
3. **Testing**: Simulate various scenarios (errors, edge cases)
4. **Training**: Safe environment for user training

### Real Mode Benefits:
1. **Production**: Connect to actual PLCs
2. **Integration**: Real-time data from manufacturing
3. **Validation**: Verify against actual equipment

## 📈 IMPLEMENTATION PRIORITY

### Phase 1 (High Priority):
- [ ] Create MockPlcService
- [ ] Add PlcMode configuration
- [ ] Implement ServiceContainer/DI

### Phase 2 (Medium Priority):
- [ ] Refactor existing services to use DI
- [ ] Add mock data scenarios
- [ ] Update documentation

### Phase 3 (Low Priority):
- [ ] Advanced mock features (time-based data)
- [ ] Mock data persistence
- [ ] Performance optimization

## 🎯 SUCCESS CRITERIA

1. **✅ No business code changes** when switching PLC modes
2. **✅ UI/ViewModel independence** from PLC implementation
3. **✅ Easy testing** without physical PLCs
4. **✅ Clear architecture** with proper separation of concerns
5. **✅ Configuration-driven** mode switching

## 📚 DOCUMENTATION NEEDED

1. **Developer Guide**: How to add new PLC types
2. **Configuration Guide**: How to switch between modes
3. **Testing Guide**: How to use mock mode for testing
4. **Architecture Diagram**: Service dependencies and data flow
