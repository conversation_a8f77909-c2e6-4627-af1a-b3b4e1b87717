using System;
using System.ComponentModel.DataAnnotations;

namespace ZoomableApp.Models
{
    /// <summary>
    /// Model cho dữ liệu sản xuất được lưu từ PLC
    /// </summary>
    public class ProductionData
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Thời gian lưu bản ghi
        /// </summary>
        public string Timestamp { get; set; } = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

        /// <summary>
        /// Trạm (ST1-ST18)
        /// </summary>
        public string Station { get; set; } = "";

        /// <summary>
        /// Số sản phẩm đạt chất lượng (D1000)
        /// </summary>
        public int Product_OK { get; set; }

        /// <summary>
        /// Số sản phẩm không đạt chất lượng (D1002)
        /// </summary>
        public int Product_NG { get; set; }

        /// <summary>
        /// Tổng số sản phẩm (D1004)
        /// </summary>
        public int Product_Total { get; set; }

        /// <summary>
        /// Thời gian hoàn thành thao tác (D72-D79)
        /// </summary>
        public float Time_Complete { get; set; }

        /// <summary>
        /// Thời gian trễ (D1080-D1114)
        /// </summary>
        public int Time_Delay { get; set; }

        /// <summary>
        /// Tổng thời gian dừng (D1132-D1182)
        /// </summary>
        public int Time_Stop { get; set; }

        /// <summary>
        /// Số lần dừng (D1190-D1215)
        /// </summary>
        public int Number_Stop { get; set; }

        /// <summary>
        /// Mã lỗi (M0, X06-X1610)
        /// </summary>
        public string Error_Code { get; set; } = "";

        /// <summary>
        /// Mô tả lỗi
        /// </summary>
        public string Error_Text { get; set; } = "";

        /// <summary>
        /// Ca làm việc
        /// </summary>
        public string WorkShift { get; set; } = "";

        /// <summary>
        /// Loại báo cáo (Production, SlowOperation, MeasureOperation, MonthlyReport, ErrorHistory)
        /// </summary>
        public string ReportType { get; set; } = "Production";

        /// <summary>
        /// Người tạo bản ghi
        /// </summary>
        public string CreatedBy { get; set; } = "";

        /// <summary>
        /// Ghi chú thêm
        /// </summary>
        public string Notes { get; set; } = "";
    }

    /// <summary>
    /// Enum cho các loại báo cáo
    /// </summary>
    public enum ReportType
    {
        Production,         // Sản lượng
        SlowOperation,      // Thao tác chậm
        MeasureOperation,   // Đo thao tác
        MonthlyReport,      // Tổng hợp tháng
        ErrorHistory        // Lịch sử lỗi
    }

    /// <summary>
    /// Enum cho ca làm việc
    /// </summary>
    public enum WorkShiftType
    {
        Morning,        // Ca sáng: 06:00-11:00
        Afternoon,      // Ca chiều: 14:00-22:00
        Administrative, // Ca hành chính: 08:00-17:00
        Night          // Ca đêm: 22:00-06:00 (hôm sau)
    }

    /// <summary>
    /// Helper class cho ca làm việc
    /// </summary>
    public static class WorkShiftHelper
    {
        /// <summary>
        /// Lấy ca làm việc hiện tại dựa trên thời gian
        /// </summary>
        public static WorkShiftType GetCurrentShift()
        {
            var now = DateTime.Now.TimeOfDay;
            
            // Ca sáng: 06:00-11:00
            if (now >= TimeSpan.FromHours(6) && now < TimeSpan.FromHours(11))
                return WorkShiftType.Morning;
            
            // Ca hành chính: 08:00-17:00 (overlap với ca sáng, ưu tiên ca sáng)
            if (now >= TimeSpan.FromHours(8) && now < TimeSpan.FromHours(17))
                return WorkShiftType.Administrative;
            
            // Ca chiều: 14:00-22:00 (overlap với ca hành chính, ưu tiên ca chiều)
            if (now >= TimeSpan.FromHours(14) && now < TimeSpan.FromHours(22))
                return WorkShiftType.Afternoon;
            
            // Ca đêm: 22:00-06:00 (hôm sau)
            return WorkShiftType.Night;
        }

        /// <summary>
        /// Lấy tên ca làm việc
        /// </summary>
        public static string GetShiftName(WorkShiftType shift)
        {
            return shift switch
            {
                WorkShiftType.Morning => "Ca sáng (06:00-11:00)",
                WorkShiftType.Afternoon => "Ca chiều (14:00-22:00)",
                WorkShiftType.Administrative => "Ca hành chính (08:00-17:00)",
                WorkShiftType.Night => "Ca đêm (22:00-06:00)",
                _ => "Không xác định"
            };
        }

        /// <summary>
        /// Kiểm tra có phải thời điểm chuyển ca không
        /// </summary>
        public static bool IsShiftChangeTime()
        {
            var now = DateTime.Now.TimeOfDay;
            var tolerance = TimeSpan.FromMinutes(1); // Dung sai 1 phút

            var shiftChangeTimes = new[]
            {
                TimeSpan.FromHours(6),   // 06:00
                TimeSpan.FromHours(8),   // 08:00
                TimeSpan.FromHours(11),  // 11:00
                TimeSpan.FromHours(14),  // 14:00
                TimeSpan.FromHours(17),  // 17:00
                TimeSpan.FromHours(22)   // 22:00
            };

            foreach (var changeTime in shiftChangeTimes)
            {
                if (Math.Abs((now - changeTime).TotalMinutes) <= tolerance.TotalMinutes)
                    return true;
            }

            return false;
        }
    }
}
