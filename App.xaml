﻿<Application x:Class="ZoomableApp.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:ZoomableApp"
             xmlns:converters="clr-namespace:ZoomableApp.Converters"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             >
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ui:ThemesDictionary Theme="Dark" />
                <ui:ControlsDictionary />
            </ResourceDictionary.MergedDictionaries>
            
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converters:EnumToVisibilityConverter x:Key="EnumToVisibilityConverter"/>
            <converters:GreaterThanToVisibilityConverter x:Key="GreaterThanToVisibilityConverter"/>
            <converters:StringContainsFailedConverter x:Key="StringContainsFailedConverter"/>
            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
            <converters:AdminRoleToVisibilityConverter x:Key="AdminRoleToVisibilityConverter"/>
            
            <!-- Text legend color-->
            <SolidColorBrush x:Key="TextLegendBrush" Color="#FFFFFFFF"/>

            <!-- Modern Color Palette -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#CC2C3E50"/>
            <SolidColorBrush x:Key="SecondaryBrush" Color="#CC34495E"/>
            <SolidColorBrush x:Key="AccentBrush" Color="#CC3498DB"/>
            <SolidColorBrush x:Key="SuccessBrush" Color="#CC27AE60"/>
            <SolidColorBrush x:Key="WarningBrush" Color="#CCF39C12"/>
            <SolidColorBrush x:Key="DangerBrush" Color="#CCE74C3C"/>
            <SolidColorBrush x:Key="InfoBrush" Color="#CC9B59B6"/>
            <SolidColorBrush x:Key="LightBrush" Color="#CCECF0F1"/>
            <SolidColorBrush x:Key="DarkBrush" Color="#CC2C3E50"/>
            <SolidColorBrush x:Key="MutedBrush" Color="#CC95A5A6"/>
            <SolidColorBrush x:Key="SurfaceBrush" Color="#CCFFFFFF"/>
            <SolidColorBrush x:Key="BorderBrush" Color="#CCBDC3C7"/>
            <SolidColorBrush x:Key="TextPrimaryBrush" Color="#CC2C3E50"/>
            <SolidColorBrush x:Key="TextSecondaryBrush" Color="#CC7F8C8D"/>
            <SolidColorBrush x:Key="TextMutedBrush" Color="#CC95A5A6"/>

            <!-- Background -->
            <ImageBrush x:Key="BackgroundBrush" ImageSource="Resources\sky.png"/>
            <!-- Blur effect -->
            <BlurEffect x:Key="BlurEffect10" Radius="10"/>
            <!-- Modern Typography -->
            <Style x:Key="HeadingLargeStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="28"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
                <Setter Property="LineHeight" Value="34"/>
            </Style>

            <Style x:Key="HeadingMediumStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="22"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="Foreground" Value="{StaticResource SurfaceBrush}"/>
                <Setter Property="LineHeight" Value="28"/>
            </Style>

            <Style x:Key="HeadingSmallStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="18"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
                <Setter Property="LineHeight" Value="24"/>
            </Style>

            <Style x:Key="BodyLargeStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="16"/>
                <Setter Property="FontWeight" Value="Normal"/>
                <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
                <Setter Property="LineHeight" Value="22"/>
            </Style>

            <Style x:Key="BodyMediumStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="FontWeight" Value="Normal"/>
                <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
                <Setter Property="LineHeight" Value="20"/>
            </Style>

            <Style x:Key="BodySmallStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="FontWeight" Value="Normal"/>
                <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
                <Setter Property="LineHeight" Value="18"/>
            </Style>

            <Style x:Key="CaptionStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="11"/>
                <Setter Property="FontWeight" Value="Normal"/>
                <Setter Property="Foreground" Value="{StaticResource TextMutedBrush}"/>
                <Setter Property="LineHeight" Value="16"/>
            </Style>

            <!-- Modern Card Style -->
            <Style x:Key="ModernCardStyle" TargetType="Border">
                <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="CornerRadius" Value="12"/>
                <Setter Property="Padding" Value="20"/>
                <Setter Property="Margin" Value="5"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="#40000000" Direction="270" ShadowDepth="4" BlurRadius="12" Opacity="0.15"/>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Modern Input Style -->
            <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
                <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
                <Setter Property="BorderThickness" Value="2"/>
                <Setter Property="Padding" Value="12,10"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="TextBox">
                            <Border x:Name="border"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="8">
                                <ScrollViewer x:Name="PART_ContentHost"
                                              Focusable="false"
                                              HorizontalScrollBarVisibility="Hidden"
                                              VerticalScrollBarVisibility="Hidden"
                                              Margin="{TemplateBinding Padding}"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="true">
                                    <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
                                </Trigger>
                                <Trigger Property="IsFocused" Value="true">
                                    <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
                                    <Setter TargetName="border" Property="Effect">
                                        <Setter.Value>
                                            <DropShadowEffect Color="#3498DB" Direction="270" ShadowDepth="0" BlurRadius="8" Opacity="0.3"/>
                                        </Setter.Value>
                                    </Setter>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Modern ComboBox Style -->
            <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
                <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
                <Setter Property="BorderThickness" Value="2"/>
                <Setter Property="Padding" Value="12,10"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="ComboBox">
                            <Grid>
                                <Border x:Name="border"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        CornerRadius="8">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <ContentPresenter Grid.Column="0"
                                                          x:Name="contentPresenter"
                                                          Content="{TemplateBinding SelectionBoxItem}"
                                                          ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                          ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                                          Margin="{TemplateBinding Padding}"
                                                          VerticalAlignment="Center"
                                                          HorizontalAlignment="Left"/>
                                        <Path Grid.Column="1"
                                              x:Name="arrow"
                                              Data="M 0 0 L 4 4 L 8 0 Z"
                                              Fill="{StaticResource TextSecondaryBrush}"
                                              Margin="0,0,12,0"
                                              VerticalAlignment="Center"/>
                                    </Grid>
                                </Border>
                                <Popup x:Name="PART_Popup"
                                       Placement="Bottom"
                                       IsOpen="{TemplateBinding IsDropDownOpen}"
                                       AllowsTransparency="True"
                                       Focusable="False"
                                       PopupAnimation="Slide">
                                    <Border Background="{StaticResource SurfaceBrush}"
                                            BorderBrush="{StaticResource BorderBrush}"
                                            BorderThickness="1"
                                            CornerRadius="8"
                                            Margin="0,4,0,0">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#40000000" Direction="270" ShadowDepth="4" BlurRadius="8" Opacity="0.2"/>
                                        </Border.Effect>
                                        <ScrollViewer x:Name="DropDownScrollViewer">
                                            <ItemsPresenter x:Name="ItemsPresenter"/>
                                        </ScrollViewer>
                                    </Border>
                                </Popup>
                            </Grid>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="true">
                                    <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
                                </Trigger>
                                <Trigger Property="IsFocused" Value="true">
                                    <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Modern DatePicker Style -->
            <Style x:Key="ModernDatePickerStyle" TargetType="DatePicker">
                <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
                <Setter Property="BorderThickness" Value="2"/>
                <Setter Property="Padding" Value="12,10"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="DatePicker">
                            <Border x:Name="border"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="8">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <DatePickerTextBox Grid.Column="0"
                                                       x:Name="PART_TextBox"
                                                       Background="Transparent"
                                                       BorderThickness="0"
                                                       Padding="{TemplateBinding Padding}"
                                                       VerticalContentAlignment="Center"/>
                                    <Button Grid.Column="1"
                                            x:Name="PART_Button"
                                            Background="Transparent"
                                            BorderThickness="0"
                                            Padding="8"
                                            Content="📅"
                                            FontSize="14"/>
                                </Grid>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="true">
                                    <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
                                </Trigger>
                                <Trigger Property="IsFocused" Value="true">
                                    <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- 3D Button Style with Shadow and Depth -->
            <Style x:Key="Modern3DButtonStyle" TargetType="Button">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Grid>
                                <!-- Shadow Layer -->
                                <Border x:Name="ShadowBorder"
                                       Background="#30000000"
                                       CornerRadius="8"
                                       Margin="0,0,-4,-4"
                                       RenderTransformOrigin="0.5,0.5">
                                    <Border.RenderTransform>
                                        <TranslateTransform x:Name="ShadowTransform" X="4" Y="4"/>
                                    </Border.RenderTransform>
                                    <Border.Effect>
                                        <BlurEffect Radius="3"/>
                                    </Border.Effect>
                                </Border>

                                <!-- Main Button -->
                                <Border x:Name="MainBorder"
                                       Background="{TemplateBinding Background}"
                                       CornerRadius="8"
                                       BorderThickness="1"
                                       RenderTransformOrigin="0.5,0.5">
                                    <Border.BorderBrush>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                            <GradientStop Color="#80FFFFFF" Offset="0"/>
                                            <GradientStop Color="#40000000" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Border.BorderBrush>

                                    <Border.RenderTransform>
                                        <TranslateTransform x:Name="MainTransform" X="0" Y="0"/>
                                    </Border.RenderTransform>

                                    <!-- Grid to hold multiple children -->
                                    <Grid>
                                        <!-- Highlight Layer for 3D Effect -->
                                        <Border CornerRadius="7" Margin="1">
                                            <Border.Background>
                                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,0.4">
                                                    <GradientStop Color="#60FFFFFF" Offset="0"/>
                                                    <GradientStop Color="Transparent" Offset="1"/>
                                                </LinearGradientBrush>
                                            </Border.Background>
                                        </Border>

                                        <!-- Content with proper padding -->
                                        <ContentPresenter x:Name="ContentPresenter"
                                                         HorizontalAlignment="Center"
                                                         VerticalAlignment="Center"
                                                         Margin="{TemplateBinding Padding}"
                                                         TextElement.FontWeight="{TemplateBinding FontWeight}"
                                                         TextElement.FontSize="{TemplateBinding FontSize}"
                                                         TextElement.Foreground="{TemplateBinding Foreground}"/>
                                    </Grid>
                                </Border>
                            </Grid>

                            <ControlTemplate.Triggers>
                                <!-- Hover Effect - Lift button slightly -->
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Trigger.EnterActions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation Storyboard.TargetName="MainTransform"
                                                               Storyboard.TargetProperty="Y"
                                                               To="-2" Duration="0:0:0.15"/>
                                                <DoubleAnimation Storyboard.TargetName="ShadowTransform"
                                                               Storyboard.TargetProperty="Y"
                                                               To="6" Duration="0:0:0.15"/>
                                                <DoubleAnimation Storyboard.TargetName="ShadowBorder"
                                                               Storyboard.TargetProperty="Opacity"
                                                               To="0.5" Duration="0:0:0.15"/>
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </Trigger.EnterActions>
                                    <Trigger.ExitActions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation Storyboard.TargetName="MainTransform"
                                                               Storyboard.TargetProperty="Y"
                                                               To="0" Duration="0:0:0.15"/>
                                                <DoubleAnimation Storyboard.TargetName="ShadowTransform"
                                                               Storyboard.TargetProperty="Y"
                                                               To="4" Duration="0:0:0.15"/>
                                                <DoubleAnimation Storyboard.TargetName="ShadowBorder"
                                                               Storyboard.TargetProperty="Opacity"
                                                               To="0.3" Duration="0:0:0.15"/>
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </Trigger.ExitActions>
                                </Trigger>

                                <!-- Press Effect - Push button down -->
                                <Trigger Property="IsPressed" Value="True">
                                    <Trigger.EnterActions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation Storyboard.TargetName="MainTransform"
                                                               Storyboard.TargetProperty="Y"
                                                               To="3" Duration="0:0:0.08"/>
                                                <DoubleAnimation Storyboard.TargetName="ShadowTransform"
                                                               Storyboard.TargetProperty="Y"
                                                               To="1" Duration="0:0:0.08"/>
                                                <DoubleAnimation Storyboard.TargetName="ShadowBorder"
                                                               Storyboard.TargetProperty="Opacity"
                                                               To="0.1" Duration="0:0:0.08"/>
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </Trigger.EnterActions>
                                    <Trigger.ExitActions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation Storyboard.TargetName="MainTransform"
                                                               Storyboard.TargetProperty="Y"
                                                               To="0" Duration="0:0:0.15"/>
                                                <DoubleAnimation Storyboard.TargetName="ShadowTransform"
                                                               Storyboard.TargetProperty="Y"
                                                               To="4" Duration="0:0:0.15"/>
                                                <DoubleAnimation Storyboard.TargetName="ShadowBorder"
                                                               Storyboard.TargetProperty="Opacity"
                                                               To="0.3" Duration="0:0:0.15"/>
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </Trigger.ExitActions>
                                </Trigger>

                                <!-- Disabled State -->
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter TargetName="MainBorder" Property="Opacity" Value="0.6"/>
                                    <Setter TargetName="ShadowBorder" Property="Opacity" Value="0.2"/>
                                    <Setter TargetName="ContentPresenter" Property="TextElement.Foreground" Value="#888"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>

                <!-- Default Properties for better text display -->
                <Setter Property="Padding" Value="20,12"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="MinWidth" Value="120"/>
                <Setter Property="MinHeight" Value="40"/>
                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
            </Style>

            <Style TargetType="DataGridColumnHeader" x:Key="CenterHeaderStyle">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderBrush" Value="Transparent"/>
                <Setter Property="Foreground" Value="Black"/>
                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Height" Value="40"/>
            </Style>

            <Style x:Key="CenterTextBlockStyle" TargetType="TextBlock">
                <Setter Property="HorizontalAlignment" Value="Center"/>
                <Setter Property="VerticalAlignment" Value="Center"/>
                <Setter Property="TextAlignment" Value="Center"/>
            </Style>
            
            <!-- Animation -->
            <VisualBrush x:Key="LoopGradientBrush1" TileMode="Tile" Viewport="0,0,1,1" ViewportUnits="RelativeToBoundingBox" Viewbox="0,0,1,1" ViewboxUnits="RelativeToBoundingBox">
                <VisualBrush.Visual>
                    <Rectangle Width="10" Height="240">
                        <Rectangle.Fill>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                <GradientStop Color="#348F50" Offset="0.0"/>
                                <GradientStop Color="#56B4D3" Offset="0.5"/>
                                <GradientStop Color="#348F50" Offset="1.0"/>
                            </LinearGradientBrush>
                        </Rectangle.Fill>
                    </Rectangle>
                </VisualBrush.Visual>
            </VisualBrush>
            <VisualBrush x:Key="LoopGradientBrush2" TileMode="Tile" Viewport="0,0,1,1" ViewportUnits="RelativeToBoundingBox" Viewbox="0,0,1,1" ViewboxUnits="RelativeToBoundingBox">
                <VisualBrush.Visual>
                    <Rectangle Width="10" Height="240">
                        <Rectangle.Fill>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                <GradientStop Color="#FF512F" Offset="0.0" />
                                <GradientStop Color="#DD2476" Offset="0.5" />
                                <GradientStop Color="#FF512F" Offset="1.0"/>
                            </LinearGradientBrush>
                        </Rectangle.Fill>
                    </Rectangle>
                </VisualBrush.Visual>
            </VisualBrush>
        </ResourceDictionary>
    </Application.Resources>
</Application>
