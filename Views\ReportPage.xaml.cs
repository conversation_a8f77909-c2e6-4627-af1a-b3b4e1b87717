using System.Windows.Controls;
using ZoomableApp.ViewModels;
using ZoomableApp.Services;
using ZoomableApp.PLC;
using ZoomableApp.Models;

namespace ZoomableApp.Views
{
    /// <summary>
    /// Interaction logic for ReportPage.xaml
    /// </summary>
    public partial class ReportPage : UserControl
    {
        public ReportPage()
        {
            try
            {
                InitializeComponent();

                // Khởi tạo ViewModel với dependencies
                // Note: Trong thực tế, nên sử dụng Dependency Injection
                // Tạm thời tạo PlcConnectionManager rỗng, sẽ được inject từ MainWindow
                var emptyConfigs = new List<PlcConnectionInfo>();
                var plcManager = new PlcConnectionManager(emptyConfigs);
                var productionDataService = new ProductionDataService(plcManager);

                DataContext = new ReportViewModel(productionDataService);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ReportPage constructor: {ex.Message}");
                // Fallback: Khởi tạo với ViewModel lỗi config
                InitializeComponent();
                DataContext = new ReportViewModel(); // Constructor không tham số sẽ hiển thị lỗi config
            }
        }

        /// <summary>
        /// Constructor với dependency injection
        /// </summary>
        public ReportPage(ProductionDataService productionDataService)
        {
            InitializeComponent();
            DataContext = new ReportViewModel(productionDataService);
        }
    }
}
