using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using ZoomableApp.Models;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Service xuất dữ liệu ra file CSV
    /// </summary>
    public class CsvExportService
    {
        private readonly ILoggerService _logger;

        private readonly string _exportPath;

        public CsvExportService()
        {
            _logger = ServiceContainer.GetService<ILoggerService>();

            // Lấy đường dẫn từ config, mặc định là C:\Project-Dat\PANA
            var config = ConfigLoader.LoadExcelSettings();
            _exportPath = Path.GetDirectoryName(config.PlanFilePath) ?? @"C:\Project-Dat\PANA";

            // Tạo thư mục nếu chưa tồn tại
            EnsureDirectoryExists(_exportPath);
        }

        /// <summary>
        /// Đả<PERSON> bả<PERSON> thư mục tồn tại
        /// </summary>
        private void EnsureDirectoryExists(string path)
        {
            try
            {
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CsvExportService: Error creating directory {path}: {ex.Message}");
            }
        }

        /// <summary>
        /// Tạo tên file với timestamp
        /// </summary>
        /// <param name="prefix">Tiền tố tên file</param>
        /// <returns>Tên file với timestamp và extension .csv</returns>
        private string GenerateTimestampedFileName(string prefix)
        {
            var timestamp = DateTime.Now.ToString("ddMMyy");
            return $"{timestamp}_{prefix}.csv";
        }

        /// <summary>
        /// Xuất DataTable ra file CSV
        /// </summary>
        /// <param name="dataTable">DataTable chứa dữ liệu</param>
        /// <param name="filePath">Đường dẫn file CSV</param>
        /// <param name="includeHeaders">Có bao gồm headers không</param>
        public void WriteDataTableToCsv(DataTable dataTable, string filePath, bool includeHeaders = true)
        {
            try
            {
                using var writer = new StreamWriter(filePath, false, Encoding.UTF8);
                
                // Ghi headers nếu cần
                if (includeHeaders && dataTable.Columns.Count > 0)
                {
                    var headers = dataTable.Columns.Cast<DataColumn>()
                        .Select(column => EscapeCsvField(column.ColumnName));
                    writer.WriteLine(string.Join(",", headers));
                }

                // Ghi dữ liệu
                foreach (DataRow row in dataTable.Rows)
                {
                    var fields = row.ItemArray.Select(field => EscapeCsvField(field?.ToString() ?? ""));
                    writer.WriteLine(string.Join(",", fields));
                }

                _logger?.LogDebug($"CsvExportService: Successfully wrote data to {filePath}");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"CsvExportService: Error writing to CSV: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Escape CSV field để tránh lỗi với dấu phẩy, quotes
        /// </summary>
        private string EscapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field))
                return "";

            // Nếu field chứa dấu phẩy, quotes, hoặc newline thì cần wrap trong quotes
            if (field.Contains(",") || field.Contains("\"") || field.Contains("\n") || field.Contains("\r"))
            {
                // Escape quotes bằng cách double quotes
                field = field.Replace("\"", "\"\"");
                return $"\"{field}\"";
            }

            return field;
        }

        /// <summary>
        /// Xuất báo cáo sản lượng ra CSV
        /// </summary>
        public string ExportProductionReport(List<ProductionData> data, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var fileName = GenerateTimestampedFileName("SanLuong");
                var filePath = Path.Combine(_exportPath, fileName);

                var dataTable = new DataTable();
                
                // Thêm columns
                dataTable.Columns.Add("STT", typeof(int));
                dataTable.Columns.Add("Thời gian", typeof(string));
                dataTable.Columns.Add("Trạm", typeof(string));
                dataTable.Columns.Add("Sản phẩm OK", typeof(int));
                dataTable.Columns.Add("Sản phẩm NG", typeof(int));
                dataTable.Columns.Add("Tổng sản phẩm", typeof(int));
                dataTable.Columns.Add("Ca làm việc", typeof(string));
                dataTable.Columns.Add("Thời gian hoàn thành", typeof(float));
                dataTable.Columns.Add("Ghi chú", typeof(string));

                // Thêm dữ liệu
                for (int i = 0; i < data.Count; i++)
                {
                    var item = data[i];
                    var row = dataTable.NewRow();
                    row["STT"] = i + 1;
                    row["Thời gian"] = item.Timestamp;
                    row["Trạm"] = item.Station;
                    row["Sản phẩm OK"] = item.Product_OK;
                    row["Sản phẩm NG"] = item.Product_NG;
                    row["Tổng sản phẩm"] = item.Product_Total;
                    row["Ca làm việc"] = item.WorkShift;
                    row["Thời gian hoàn thành"] = item.Time_Complete;
                    row["Ghi chú"] = item.Notes ?? "";
                    dataTable.Rows.Add(row);
                }

                WriteDataTableToCsv(dataTable, filePath);
                return filePath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CsvExportService: Error exporting production report: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Xuất báo cáo thao tác chậm ra CSV
        /// </summary>
        public string ExportSlowOperationReport(List<ProductionData> data)
        {
            try
            {
                var fileName = GenerateTimestampedFileName("ThaoTacCham");
                var filePath = Path.Combine(_exportPath, fileName);

                var dataTable = new DataTable();
                
                // Thêm columns
                dataTable.Columns.Add("STT", typeof(int));
                dataTable.Columns.Add("Thời gian", typeof(string));
                dataTable.Columns.Add("Trạm", typeof(string));
                dataTable.Columns.Add("Thời gian hoàn thành", typeof(string));
                dataTable.Columns.Add("Thời gian trễ", typeof(string));
                dataTable.Columns.Add("Ca làm việc", typeof(string));
                dataTable.Columns.Add("Mã lỗi", typeof(string));
                dataTable.Columns.Add("Ghi chú", typeof(string));

                // Filter slow operations (time delay > 0)
                var slowOps = data.Where(d => d.Time_Delay > 0).ToList();

                // Thêm dữ liệu
                for (int i = 0; i < slowOps.Count; i++)
                {
                    var item = slowOps[i];
                    var row = dataTable.NewRow();
                    row["STT"] = i + 1;
                    row["Thời gian"] = item.Timestamp;
                    row["Trạm"] = item.Station;
                    row["Thời gian hoàn thành"] = $"{item.Time_Complete:F1}s";
                    row["Thời gian trễ"] = $"{item.Time_Delay} ms";
                    row["Ca làm việc"] = item.WorkShift;
                    row["Mã lỗi"] = item.Error_Code ?? "";
                    row["Ghi chú"] = item.Notes ?? "";
                    dataTable.Rows.Add(row);
                }

                WriteDataTableToCsv(dataTable, filePath);
                return filePath;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"CsvExportService: Error exporting slow operation report: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Xuất báo cáo đo thao tác ra CSV
        /// </summary>
        public string ExportMeasureOperationReport(List<ProductionData> data)
        {
            try
            {
                var fileName = GenerateTimestampedFileName("DoThaoTac");
                var filePath = Path.Combine(_exportPath, fileName);

                var dataTable = new DataTable();
                
                // Thêm columns
                dataTable.Columns.Add("STT", typeof(int));
                dataTable.Columns.Add("Vị trí", typeof(string));
                dataTable.Columns.Add("Lần 1", typeof(string));
                dataTable.Columns.Add("Lần 2", typeof(string));
                dataTable.Columns.Add("Lần 3", typeof(string));
                dataTable.Columns.Add("Lần 4", typeof(string));
                dataTable.Columns.Add("Lần 5", typeof(string));
                dataTable.Columns.Add("Lần 6", typeof(string));
                dataTable.Columns.Add("Lần 7", typeof(string));
                dataTable.Columns.Add("Lần 8", typeof(string));
                dataTable.Columns.Add("Lần 9", typeof(string));
                dataTable.Columns.Add("Lần 10", typeof(string));
                dataTable.Columns.Add("Trung bình", typeof(string));

                // Tạo dữ liệu mẫu cho 18 vị trí
                var random = new Random();
                for (int i = 1; i <= 18; i++)
                {
                    var row = dataTable.NewRow();
                    row["STT"] = i;
                    row["Vị trí"] = $"Vị trí {i}";
                    
                    var times = new List<double>();
                    for (int j = 1; j <= 10; j++)
                    {
                        var time = random.NextDouble() * 10 + 15; // 15-25 giây
                        times.Add(time);
                        row[$"Lần {j}"] = $"{time:F2}s";
                    }
                    
                    row["Trung bình"] = $"{times.Average():F2}s";
                    dataTable.Rows.Add(row);
                }

                WriteDataTableToCsv(dataTable, filePath);
                return filePath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CsvExportService: Error exporting measure operation report: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Xuất báo cáo tổng hợp tháng ra CSV
        /// </summary>
        public string ExportMonthlyReport(List<ProductionData> data, int month, int year)
        {
            try
            {
                var fileName = GenerateTimestampedFileName("TongHopThang");
                var filePath = Path.Combine(_exportPath, fileName);

                var dataTable = new DataTable();
                
                // Thêm columns
                dataTable.Columns.Add("Ngày", typeof(string));
                dataTable.Columns.Add("Ca sáng", typeof(int));
                dataTable.Columns.Add("Ca chiều", typeof(int));
                dataTable.Columns.Add("Ca đêm", typeof(int));
                dataTable.Columns.Add("Tổng ngày", typeof(int));
                dataTable.Columns.Add("Kế hoạch", typeof(int));
                dataTable.Columns.Add("Đạt (%)", typeof(string));
                dataTable.Columns.Add("Chất lượng (%)", typeof(string));
                dataTable.Columns.Add("Ghi chú", typeof(string));

                // Tạo dữ liệu cho từng ngày trong tháng
                var daysInMonth = DateTime.DaysInMonth(year, month);
                var random = new Random();
                
                for (int day = 1; day <= daysInMonth; day++)
                {
                    var row = dataTable.NewRow();
                    var date = new DateTime(year, month, day);
                    
                    row["Ngày"] = date.ToString("dd/MM/yyyy");
                    
                    var morning = random.Next(80, 120);
                    var afternoon = random.Next(80, 120);
                    var night = random.Next(70, 110);
                    var total = morning + afternoon + night;
                    var plan = random.Next(250, 350);
                    
                    row["Ca sáng"] = morning;
                    row["Ca chiều"] = afternoon;
                    row["Ca đêm"] = night;
                    row["Tổng ngày"] = total;
                    row["Kế hoạch"] = plan;
                    row["Đạt (%)"] = $"{(double)total / plan * 100:F1}%";
                    row["Chất lượng (%)"] = $"{random.Next(92, 98)}.{random.Next(0, 9)}%";
                    row["Ghi chú"] = "";
                    
                    dataTable.Rows.Add(row);
                }

                WriteDataTableToCsv(dataTable, filePath);
                return filePath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CsvExportService: Error exporting monthly report: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Xuất báo cáo lịch sử lỗi ra CSV
        /// </summary>
        public string ExportErrorHistoryReport(List<ProductionData> data)
        {
            try
            {
                var fileName = GenerateTimestampedFileName("LichSuLoi");
                var filePath = Path.Combine(_exportPath, fileName);

                var dataTable = new DataTable();
                
                // Thêm columns
                dataTable.Columns.Add("STT", typeof(int));
                dataTable.Columns.Add("Thời gian", typeof(string));
                dataTable.Columns.Add("Trạm", typeof(string));
                dataTable.Columns.Add("Mã lỗi", typeof(string));
                dataTable.Columns.Add("Mô tả lỗi", typeof(string));
                dataTable.Columns.Add("Ca làm việc", typeof(string));
                dataTable.Columns.Add("Người tạo", typeof(string));
                dataTable.Columns.Add("Ghi chú", typeof(string));

                // Filter data with errors
                var errorData = data.Where(d => !string.IsNullOrEmpty(d.Error_Code)).ToList();

                // Thêm dữ liệu
                for (int i = 0; i < errorData.Count; i++)
                {
                    var item = errorData[i];
                    var row = dataTable.NewRow();
                    row["STT"] = i + 1;
                    row["Thời gian"] = item.Timestamp;
                    row["Trạm"] = item.Station;
                    row["Mã lỗi"] = item.Error_Code ?? "";
                    row["Mô tả lỗi"] = GetErrorDescription(item.Error_Code);
                    row["Ca làm việc"] = item.WorkShift;
                    row["Người tạo"] = item.CreatedBy ?? "System";
                    row["Ghi chú"] = item.Notes ?? "";
                    dataTable.Rows.Add(row);
                }

                WriteDataTableToCsv(dataTable, filePath);
                return filePath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CsvExportService: Error exporting error history report: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Lấy mô tả lỗi từ mã lỗi
        /// </summary>
        private string GetErrorDescription(string errorCode)
        {
            return errorCode switch
            {
                "E001" => "Lỗi cảm biến",
                "E002" => "Lỗi động cơ",
                "E003" => "Lỗi kết nối",
                "E004" => "Lỗi chất lượng",
                "E005" => "Lỗi thời gian",
                _ => "Lỗi không xác định"
            };
        }
    }
}
