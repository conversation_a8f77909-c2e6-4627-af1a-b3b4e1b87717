using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using ZoomableApp.Services;

namespace ZoomableApp.ViewModels
{
    public class HomeDailyPlanViewModel : INotifyPropertyChanged
    {
        private readonly PlanViewModel _planViewModel;
        private readonly ILoggerService _logger;
        private ObservableCollection<DailyPlanItem> _dailyPlanItems;
        private DailyPlanItem _selectedItem;
        private DailyPlanItem _currentWorkingItem;
        private string _todayDateText = "";
        private string _currentProductText = "";
        private string _editableModelName = "";
        private bool _isEditingModel = false;

        public HomeDailyPlanViewModel(PlanViewModel planViewModel)
        {
            // Kiểm tra xem ServiceContainer đã được khởi tạo chưa
            if (ServiceContainer.IsRegistered<ILoggerService>())
            {
                _logger = ServiceContainer.GetService<ILoggerService>();
                _logger.LogInfo("[HomeVM] CONSTRUCTOR: ViewModel is being created.");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("[HomeVM] CONSTRUCTOR: ViewModel is being created.");
            }

            _planViewModel = planViewModel ?? throw new ArgumentNullException(nameof(planViewModel));
            _dailyPlanItems = new ObservableCollection<DailyPlanItem>();

            // Initialize commands
            RefreshCommand = new RelayCommand(LoadDailyPlan);
            MarkCompleteCommand = new RelayCommand(MarkCurrentComplete, CanMarkComplete);
            StartNextCommand = new RelayCommand(StartSelectedItem, CanStartNext);
            StartEditModelCommand = new RelayCommand(StartEditModel);
            SaveModelCommand = new RelayCommand(SaveModel, CanSaveModel);
            CancelEditModelCommand = new RelayCommand(CancelEditModel);

            // Đăng ký lắng nghe sự kiện
            _planViewModel.PropertyChanged += PlanViewModel_PropertyChanged;

            System.Diagnostics.Debug.WriteLine("[HomeVM] CONSTRUCTOR: Calling RefreshData to get initial data...");
            RefreshData();
        }

        private void RefreshData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[HomeVM] RefreshData STARTING. Reading data from PlanViewModel...");
                var dailyData = _planViewModel.DailyPlanData;

                if (dailyData == null)
                {
                    System.Diagnostics.Debug.WriteLine("[HomeVM] RefreshData: PlanViewModel.DailyPlanData is NULL. Clearing items.");
                    DailyPlanItems.Clear();
                    return;
                }

                DailyPlanItems.Clear();
                foreach (DataRow row in dailyData.Rows)
                {
                    // === SỬA LẠI CÁC TÊN CỘT CHO KHỚP VỚI LOG DEBUG ===
                    var item = new DailyPlanItem
                    {
                        // Dựa vào các file trước, tên cột có thể là 'Seq' hoặc 'No'.
                        // Hãy dùng tên cột bạn thấy trong log.
                        No = row["No"]?.ToString() ?? "",
                        Type = row["Type"]?.ToString() ?? "",
                        ModelName = row["Model name"]?.ToString() ?? "",
                        Market = row["Market"]?.ToString() ?? "",
                        Quantity = row["Q'ty"]?.ToString() ?? "", // Chú ý ký tự '
                        StartTime = row["Start time"]?.ToString() ?? "",
                        StopTime = row["Stop time"]?.ToString() ?? "",
                        Status = PlanItemStatus.NotStarted
                    };
                    DailyPlanItems.Add(item);
                }

                System.Diagnostics.Debug.WriteLine($"[HomeVM] RefreshData FINISHED. Added {DailyPlanItems.Count} items.");

                // === SỬA LOGIC CẬP NHẬT SẢN PHẨM HIỆN TẠI ===
                if (DailyPlanItems.Any())
                {
                    // Lấy item đầu tiên làm sản phẩm đang làm việc
                    CurrentWorkingItem = DailyPlanItems.First();
                    CurrentWorkingItem.Status = PlanItemStatus.InProgress;

                    // Trực tiếp cập nhật Text từ item này
                    CurrentProductText = CurrentWorkingItem.ModelName;
                    System.Diagnostics.Debug.WriteLine($"[HomeVM] Current product set to: '{CurrentProductText}'");
                }
                else
                {
                    CurrentWorkingItem = null;
                    CurrentProductText = "Không có sản phẩm";
                }
                System.Diagnostics.Debug.WriteLine($"[HomeVM] RefreshData FINISHED. Added {DailyPlanItems.Count} items to its own collection.");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[HomeVM] RefreshData ERROR: {ex.Message}");
            }
        }

        private void PlanViewModel_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(PlanViewModel.DailyPlanData))
            {
                System.Diagnostics.Debug.WriteLine("[HomeVM] EVENT HANDLER: Received notification that PlanViewModel.DailyPlanData changed. Calling RefreshData...");
                RefreshData();
            }
        }

        #region Properties

        public ObservableCollection<DailyPlanItem> DailyPlanItems
        {
            get => _dailyPlanItems;
            set
            {
                _dailyPlanItems = value;
                OnPropertyChanged();
            }
        }

        public DailyPlanItem SelectedItem
        {
            get => _selectedItem;
            set
            {
                _selectedItem = value;
                OnPropertyChanged();
                UpdateCurrentProductText();
            }
        }

        public DailyPlanItem CurrentWorkingItem
        {
            get => _currentWorkingItem;
            set
            {
                _currentWorkingItem = value;
                OnPropertyChanged();
                UpdateCurrentProductText();
            }
        }

        public string TodayDateText
        {
            get => _todayDateText;
            set
            {
                _todayDateText = value;
                OnPropertyChanged();
            }
        }

        public string CurrentProductText
        {
            get => _currentProductText;
            set
            {
                _currentProductText = value;
                OnPropertyChanged();
            }
        }

        public string EditableModelName
        {
            get => _editableModelName;
            set
            {
                _editableModelName = value;
                OnPropertyChanged();
            }
        }

        public bool IsEditingModel
        {
            get => _isEditingModel;
            set
            {
                _isEditingModel = value;
                OnPropertyChanged();
            }
        }

        public ICommand RefreshCommand { get; private set; }
        public ICommand StartEditModelCommand { get; private set; }
        public ICommand SaveModelCommand { get; private set; }
        public ICommand CancelEditModelCommand { get; private set; }
        public ICommand MarkCompleteCommand { get; private set; }
        public ICommand StartNextCommand { get; private set; }

        #endregion

        #region Methods

        private void LoadDailyPlan()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("HomeDailyPlan: LoadDailyPlan called - refreshing PlanViewModel");

                // Refresh PlanViewModel data first
                if (_planViewModel.RefreshCommand.CanExecute(null))
                {
                    _planViewModel.RefreshCommand.Execute(null);
                }

                // Then load from PlanViewModel
                LoadDailyPlanFromPlanViewModel();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"HomeDailyPlan: Error in LoadDailyPlan: {ex.Message}");
                DailyPlanItems.Clear();
                CurrentProductText = "Lỗi tải dữ liệu";
            }
        }

        private void LoadDailyPlanFromPlanViewModel()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("HomeDailyPlan: LoadDailyPlanFromPlanViewModel called");

                var dailyData = _planViewModel.DailyPlanData;
                if (dailyData == null || dailyData.Rows.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("HomeDailyPlan: No daily data available from PlanViewModel");
                    DailyPlanItems.Clear();
                    CurrentProductText = "Không có kế hoạch hôm nay";
                    return;
                }

                // Debug: Print column names
                System.Diagnostics.Debug.WriteLine("HomeDailyPlan: Available columns:");
                foreach (DataColumn column in dailyData.Columns)
                {
                    System.Diagnostics.Debug.WriteLine($"  - '{column.ColumnName}'");
                }
                
                DailyPlanItems.Clear();
                
                foreach (DataRow row in dailyData.Rows)
                {
                    var item = new DailyPlanItem
                    {
                        No = row["No"]?.ToString() ?? "",
                        Type = row["Type"]?.ToString() ?? "",
                        ModelName = row["Model name"]?.ToString() ?? "",
                        Market = row["Market"]?.ToString() ?? "",
                        Quantity = row["Q'ty"]?.ToString() ?? "",
                        StartTime = row["Start time"]?.ToString() ?? "",
                        StopTime = row["Stop time"]?.ToString() ?? "",
                        Status = PlanItemStatus.NotStarted
                    };
                    
                    DailyPlanItems.Add(item);
                }

                // Update current product if we have items
                if (DailyPlanItems.Count > 0)
                {
                    var firstItem = DailyPlanItems.FirstOrDefault();
                    CurrentProductText = firstItem?.ModelName ?? "Không xác định";

                    // Set first item as current working if no current item
                    if (CurrentWorkingItem == null)
                    {
                        CurrentWorkingItem = DailyPlanItems.First();
                        CurrentWorkingItem.Status = PlanItemStatus.InProgress;
                    }
                }
                else
                {
                    CurrentProductText = "Không có kế hoạch";
                }

                System.Diagnostics.Debug.WriteLine($"HomeDailyPlan: Loaded {DailyPlanItems.Count} items from PlanViewModel daily data");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"HomeDailyPlan: Error loading daily plan from PlanViewModel: {ex.Message}");
                DailyPlanItems.Clear();
                CurrentProductText = "Lỗi tải dữ liệu";
            }
        }



        private void UpdateCurrentProductText()
        {
            if (CurrentWorkingItem != null)
            {
                CurrentProductText = CurrentWorkingItem.ModelName;
            }
            else
            {
                CurrentProductText = "Chưa có sản phẩm";
            }
        }

        private void MarkCurrentComplete()
        {
            if (CurrentWorkingItem != null)
            {
                CurrentWorkingItem.Status = PlanItemStatus.Completed;
                CurrentWorkingItem = null;
                UpdateCurrentProductText();
            }
        }

        private bool CanMarkComplete()
        {
            return CurrentWorkingItem != null && CurrentWorkingItem.Status == PlanItemStatus.InProgress;
        }

        private void StartSelectedItem()
        {
            if (SelectedItem != null && SelectedItem.Status == PlanItemStatus.NotStarted)
            {
                SelectedItem.Status = PlanItemStatus.InProgress;
                CurrentWorkingItem = SelectedItem;
                SelectedItem = null;
            }
        }

        private bool CanStartNext()
        {
            return SelectedItem != null && 
                   SelectedItem.Status == PlanItemStatus.NotStarted && 
                   CurrentWorkingItem == null;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void StartEditModel()
        {
            EditableModelName = CurrentProductText;
            IsEditingModel = true;
        }

        private void SaveModel()
        {
            if (!string.IsNullOrWhiteSpace(EditableModelName))
            {
                CurrentProductText = EditableModelName.Trim();

                // Update the current working item if it exists
                if (CurrentWorkingItem != null)
                {
                    CurrentWorkingItem.ModelName = CurrentProductText;
                }

                IsEditingModel = false;
                System.Diagnostics.Debug.WriteLine($"[HomeVM] Model name updated to: '{CurrentProductText}'");
            }
        }

        private bool CanSaveModel()
        {
            return IsEditingModel && !string.IsNullOrWhiteSpace(EditableModelName);
        }

        private void CancelEditModel()
        {
            EditableModelName = "";
            IsEditingModel = false;
        }

        #endregion
    }

    public class DailyPlanItem : INotifyPropertyChanged
    {
        private PlanItemStatus _status;

        public string No { get; set; } = "";
        public string Type { get; set; } = "";
        public string ModelName { get; set; } = "";
        public string Market { get; set; } = "";
        public string Quantity { get; set; } = "";
        public string StartTime { get; set; } = "";
        public string StopTime { get; set; } = "";

        public PlanItemStatus Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public enum PlanItemStatus
    {
        NotStarted,    // Chưa bắt đầu - màu nền bình thường
        Selected,      // Được chọn - màu xanh dương  
        InProgress,    // Đang làm - màu vàng
        Completed      // Hoàn thành - màu xanh lá
    }
}
