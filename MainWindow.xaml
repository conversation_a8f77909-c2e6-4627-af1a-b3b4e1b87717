﻿<Window x:Class="ZoomableApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:lvc="clr-namespace:LiveChartsCore.SkiaSharpView.WPF;assembly=LiveChartsCore.SkiaSharpView.WPF"
        xmlns:local="clr-namespace:ZoomableApp.Views"
        xmlns:controls="clr-namespace:ZoomableApp.SharedControls"
        xmlns:converters="clr-namespace:ZoomableApp.Converters"
        xmlns:ViewModels="clr-namespace:ZoomableApp.ViewModels"
        xmlns:views="clr-namespace:ZoomableApp.Views"
        mc:Ignorable="d"
        Title="Hệ thống Quản lý <PERSON>n xuất" Height="900" Width="1400"
        x:Name="mainWindow"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized">
    <Window.Resources>
        <!-- Bell Shake Animation -->
        <Storyboard x:Key="BellShakeStoryboard" RepeatBehavior="Forever">
            <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="Angle">
                <EasingDoubleKeyFrame KeyTime="0:0:0.0" Value="0"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.1" Value="-15"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="15"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="-10"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="10"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="-5"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="5"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="0"/>
                <EasingDoubleKeyFrame KeyTime="0:0:1.5" Value="0"/>
                <!-- Pause -->
            </DoubleAnimationUsingKeyFrames>
        </Storyboard>
        <Storyboard x:Key="ResetBellAngleStoryboard">
            <DoubleAnimation Storyboard.TargetProperty="Angle" To="0" Duration="0:0:0.01"/>
        </Storyboard>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        <converters:StringContainsFailedConverter x:Key="StringContainsFailedConverter"/>
        <converters:PlanItemStatusToColorConverter x:Key="StatusToColorConverter"/>
        <converters:PlanItemStatusToTextColorConverter x:Key="StatusToTextColorConverter"/>
        <converters:RowIndexConverter x:Key="RowIndexConverter"/>

        <!-- Button Style with Press Effect -->
        <Style x:Key="RoundButtonStyle" TargetType="Button">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder"
                                Background="{TemplateBinding Background}"
                                CornerRadius="17"
                                BorderThickness="0"
                                RenderTransformOrigin="0.5,0.5">
                            <Border.RenderTransform>
                                <ScaleTransform x:Name="ButtonScale" ScaleX="1" ScaleY="1"/>
                            </Border.RenderTransform>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsPressed" Value="True">
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="ButtonScale"
                                                           Storyboard.TargetProperty="ScaleX"
                                                           To="0.95" Duration="0:0:0.1"/>
                                            <DoubleAnimation Storyboard.TargetName="ButtonScale"
                                                           Storyboard.TargetProperty="ScaleY"
                                                           To="0.95" Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                                <Trigger.ExitActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="ButtonScale"
                                                           Storyboard.TargetProperty="ScaleX"
                                                           To="1.0" Duration="0:0:0.1"/>
                                            <DoubleAnimation Storyboard.TargetName="ButtonScale"
                                                           Storyboard.TargetProperty="ScaleY"
                                                           To="1.0" Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.ExitActions>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style x:Key="CloseButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Width" Value="30"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="15">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#CC0000"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="1*"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition x:Name="SidebarColumn" Width="Auto"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Lớp phủ mờ -->
        <Rectangle Grid.Row="0" Grid.Column="0" Grid.RowSpan="2" Grid.ColumnSpan="2" Panel.ZIndex="-1">
            <Rectangle.Fill>
                <StaticResource ResourceKey="BackgroundBrush"/>
            </Rectangle.Fill>
            <Rectangle.Effect>
                <BlurEffect Radius="50"/>
            </Rectangle.Effect>
        </Rectangle>

        <!-- Sidebar Menu (Grid.Column="0") -->
        <Border Grid.Column="0" BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,0,1,0" MinWidth="200">
            <Border.Effect>
                <DropShadowEffect Color="#40000000" Direction="0" ShadowDepth="6" BlurRadius="12" Opacity="0.2"/>
            </Border.Effect>
            <Grid x:Name="SidebarPanel" Visibility="Collapsed" Width="200">
                <!-- Close Button - Absolute positioned -->
                <Button HorizontalAlignment="Right" VerticalAlignment="Top"
                        Margin="0,10,10,0" Style="{StaticResource CloseButtonStyle}"
                        Click="CloseSidebarButton_Click" Cursor="Hand" ToolTip="Đóng menu"
                        Panel.ZIndex="10">
                    <Path Data="M18,6L6,18M6,6L18,18" Stroke="White" StrokeThickness="2"
                          Width="16" Height="16" Stretch="Uniform"/>
                </Button>

                <!-- User Info Section - Absolute positioned -->
                <Border HorizontalAlignment="Stretch" VerticalAlignment="Top" MaxWidth="250"
                        Background="{StaticResource SecondaryBrush}" Padding="10" Margin="0,50,0,0"
                        CornerRadius="0,0,8,8" MinHeight="80">
                    <Border.Effect>
                        <DropShadowEffect Color="#40000000" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.2"/>
                    </Border.Effect>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Avatar -->
                        <Border Grid.Column="0" Width="40" Height="40" CornerRadius="20" Background="{StaticResource AccentBrush}"
                                VerticalAlignment="Top" Margin="0,0,8,0">
                            <Path Data="M12,12c2.21,0 4,-1.79 4,-4s-1.79,-4 -4,-4 -4,1.79 -4,4 1.79,4 4,4zM12,14c-2.67,0 -8,1.34 -8,4v2h16v-2c0,-2.66 -5.33,-4 -8,-4z"
                                  Fill="White" Stretch="Uniform" Width="22" Height="22"/>
                        </Border>

                        <!-- User Details -->
                        <StackPanel Grid.Column="1" VerticalAlignment="Top">
                            <TextBlock x:Name="UserFullNameTextBlock" Text="Admin User"
                                       Foreground="White" FontWeight="Bold" FontSize="12"
                                       TextWrapping="Wrap" MaxWidth="200"/>
                            <TextBlock x:Name="UserShiftTextBlock" Text="Ca hành chính: 08:00-17:00"
                                       Foreground="#BDC3C7" FontSize="10" Margin="0,2,0,0"
                                       TextWrapping="Wrap" LineHeight="12" MaxWidth="200"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Menu Items - Top aligned with proper spacing -->
                <ListBox x:Name="SidebarMenuListBox" Background="Transparent" BorderThickness="0"
                         SelectionMode="Single" Foreground="White" FontSize="14"
                         SelectionChanged="SidebarMenuListBox_SelectionChanged"
                         VerticalAlignment="Top" HorizontalAlignment="Stretch"
                         Margin="0,140,0,60">
                    <ListBox.ItemContainerStyle>
                        <Style TargetType="ListBoxItem">
                            <Setter Property="Padding" Value="15,10"/>
                            <Setter Property="Background" Value="Transparent"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Foreground" Value="White"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#3498DB"/>
                                </Trigger>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="#2980B9"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </ListBox.ItemContainerStyle>
                    <ListBoxItem Content="🏠 Trang chủ"/>
                    <ListBoxItem Content="📋 Kế hoạch"/>
                    <ListBoxItem Content="📊 Báo cáo"/>
                    <ListBoxItem Content="🔧 Bảo dưỡng"/>
                    <ListBoxItem Content="⚙️ Cài đặt"/>
                    <ListBoxItem Content="👥 Người dùng"/>
                </ListBox>

                <!-- Logout Button - Absolute positioned at bottom -->
                <Button x:Name="LogoutButton" Content="🚪 Đăng xuất"
                        Background="#E74C3C" Foreground="White"
                        Margin="15,0,15,15" FontSize="12" Height="Auto"
                        Click="LogoutButton_Click"
                        HorizontalAlignment="Stretch" VerticalAlignment="Bottom"
                        Style="{StaticResource Modern3DButtonStyle}"/>
            </Grid>
        </Border>
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <!-- Header cố định -->
                <RowDefinition Height="*"/>
                <!-- Vùng nội dung chính -->
            </Grid.RowDefinitions>

            <!-- Header cố định -->
            <Grid Grid.Row="0" Height="60">
                <Grid.Effect>
                    <DropShadowEffect Color="#40000000" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.1"/>
                </Grid.Effect>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Nút mở sidebar -->
                <Button x:Name="OpenSidebarButton" Grid.Column="0"
                        Width="50" Height="50"
                        Background="Transparent"
                        BorderThickness="0"
                        Foreground="{StaticResource SurfaceBrush}"
                        VerticalAlignment="Center"
                        Margin="15,0,0,0"
                        Click="OpenSidebarButton_Click"
                        Cursor="Hand"
                        Visibility="{Binding IsSidebarOpen, ElementName=mainWindow, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                    <Path Data="M0,5 L25,5 M0,12.5 L25,12.5 M0,20 L25,20"
                          Width="38"
                          Stroke="{StaticResource SurfaceBrush}"
                          StrokeThickness="3"
                          Stretch="Uniform"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Center" />
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="{StaticResource LightBrush}"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>

                <!-- Tên trang ở giữa -->
                <TextBlock x:Name="PageTitleTextBlock" Grid.Column="1" Text="🏠 Trang chủ"
                           Style="{StaticResource HeadingMediumStyle}"
                           HorizontalAlignment="Center" VerticalAlignment="Center"/>

                <!-- Bell notification ở bên phải -->
                <Grid Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Center" Margin="0,0,10,0">
                    <Button x:Name="ErrorBellButton" Width="40" Height="40" Background="Transparent"
                BorderThickness="0" Click="ErrorBellButton_Click" ToolTip="View Errors">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Grid>
                                    <Path x:Name="BellPath"
                              Data="M12,22c1.1,0 2,-0.9 2,-2h-4c0,1.1 0.9,2 2,2z M18,16v-5c0,-3.07 -1.63,-5.64 -4.5,-6.32L13.5,4c0,-0.83 -0.67,-1.5 -1.5,-1.5s-1.5,0.67 -1.5,1.5v0.68C7.63,5.36 6,7.92 6,11v5l-2,2v1h16v-1l-2,-2z M16,17H8v-6c0,-2.48 1.51,-4.5 4,-4.5s4,2.02 4,4.5V17z"
                              Fill="DimGray" Stretch="Uniform" RenderTransformOrigin="0.5,0.1">
                                        <Path.RenderTransform>
                                            <RotateTransform x:Name="BellButtonTransform" Angle="0"/>
                                        </Path.RenderTransform>
                                    </Path>
                                </Grid>

                                <ControlTemplate.Triggers>
                                    <DataTrigger Binding="{Binding HasNewErrors, ElementName=mainWindow}" Value="True">
                                        <Setter TargetName="BellPath" Property="Fill" Value="Red"/>
                                        <DataTrigger.EnterActions>
                                            <BeginStoryboard x:Name="BellShakeBeginStoryboard">
                                                <!-- Use an inline Storyboard -->
                                                <BeginStoryboard.Storyboard>
                                                    <Storyboard RepeatBehavior="Forever">
                                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="BellPath"
                                                                           Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)">
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.0" Value="0"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.1" Value="-15"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="15"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="-10"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="10"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="-5"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="5"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="0"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:1.5" Value="0"/>
                                                            <!-- Pause -->
                                                        </DoubleAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </BeginStoryboard.Storyboard>
                                            </BeginStoryboard>
                                        </DataTrigger.EnterActions>
                                        <DataTrigger.ExitActions>
                                            <StopStoryboard BeginStoryboardName="BellShakeBeginStoryboard"/>
                                            <BeginStoryboard x:Name="BellResetAngleBeginStoryboard">
                                                <!-- Use an inline Storyboard -->
                                                <BeginStoryboard.Storyboard>
                                                    <Storyboard>
                                                        <DoubleAnimation Storyboard.TargetName="BellPath"
                                                             Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                                                             To="0" Duration="0:0:0.01"/>
                                                    </Storyboard>
                                                </BeginStoryboard.Storyboard>
                                            </BeginStoryboard>
                                        </DataTrigger.ExitActions>
                                    </DataTrigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>

                    <!-- Badge hiển thị số lượng lỗi -->
                    <Border x:Name="ErrorCountBadge" Background="Red" CornerRadius="8"
                Height="16" MinWidth="16" Padding="3,0"
                HorizontalAlignment="Right" VerticalAlignment="Top" Margin="0,-5,-5,0"
                Visibility="{Binding HasNewErrors, ElementName=mainWindow, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock Text="{Binding NewErrorCount, ElementName=mainWindow}" Foreground="White"
                       FontSize="10" FontWeight="Bold"
                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                </Grid>
            </Grid>

            <!-- Hàng dưới: Vùng nội dung chính -->
            <Grid Grid.Row="1" x:Name="MainContentGrid">
                <!-- Toast Notification Area (Bottom Right of Main Content) -->
                <Grid x:Name="ToastNotificationContainer" Panel.ZIndex="1000"
                      VerticalAlignment="Bottom" HorizontalAlignment="Right"
                      Margin="0,0,20,20"/>
                <!-- Trang Zoom/Pan (mặc định) -->
                <Grid x:Name="ZoomPanPage" Visibility="Visible">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="2*"/>
                        <!-- Zoom/Pan area - 1/3 -->
                        <RowDefinition Height="3*"/>
                        <!-- Bottom area - 2/3 -->
                        <RowDefinition Height="6*"/>
                        <!-- Bottom area - 2/3 -->
                    </Grid.RowDefinitions>

                    <!-- Zoom/Pan Area -->
                    <Grid Grid.Row="0" Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="5*"/>
                            <ColumnDefinition Width="6*"/>
                            <ColumnDefinition Width="554*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <!-- Controls -->
                            <RowDefinition Height="*"/>
                            <!-- Viewport -->
                        </Grid.RowDefinitions>

                        <!-- Layout controls -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10" Grid.ColumnSpan="3" HorizontalAlignment="Center">
                            <TextBlock x:Name="CurrentLayoutTextBlock" Text="Hệ thống: Mainline" VerticalAlignment="Center" Margin="0,0,20,0" Foreground="{StaticResource SurfaceBrush}" FontWeight="Bold"/>
                            <Button Content="Đặt lại" Margin="0,0,20,0" Click="ResetButton_Click"/>

                            <!-- Lỗi Mainline -->
                            <TextBlock VerticalAlignment="Center" Margin="0,0,20,0" Foreground="{StaticResource SurfaceBrush}">
                                <Run Text="Mainline: "/>
                                <Run x:Name="MainlineFaultRun" Text="Không có lỗi" Foreground="#4ECDC4"/>
                            </TextBlock>

                            <!-- Lỗi Inspection -->
                            <TextBlock VerticalAlignment="Center" Margin="0,0,20,0" Foreground="{StaticResource SurfaceBrush}">
                                <Run Text="Inspection: "/>
                                <Run x:Name="InspectionFaultRun" Text="Không có lỗi" Foreground="#4ECDC4"/>
                            </TextBlock>
                        </StackPanel>

                        <!-- Viewport -->
                        <Border CornerRadius="10"  Grid.Row="1" x:Name="ViewportBorder"
                            BorderBrush="Gray" BorderThickness="2"
                            ClipToBounds="True"
                            MouseWheel="ViewportBorder_MouseWheel"
                            MouseDown="ViewportBorder_MouseDown"
                            MouseMove="ViewportBorder_MouseMove"
                            MouseUp="ViewportBorder_MouseUp" Grid.ColumnSpan="3">

                            <!-- Vùng nội dung sẽ được zoom/pan -->
                            <Canvas x:Name="ZoomPanCanvas"
                                Width="2500" Height="300"
                                >
                                <Canvas.RenderTransform>
                                    <TransformGroup>
                                        <ScaleTransform x:Name="ViewScaleTransform" ScaleX="0.5" ScaleY="0.5"/>
                                        <TranslateTransform x:Name="ViewTranslateTransform" X="0" Y="0"/>
                                    </TransformGroup>
                                </Canvas.RenderTransform>
                                <!-- NỘI DUNG SẼ ĐƯỢC TẢI ĐỘNG Ở ĐÂY -->
                            </Canvas>
                        </Border>
                    </Grid>

                    <Grid Grid.Row="1" Margin="10,0,10,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="3*"/>
                            <ColumnDefinition Width="10"/>
                            <!-- Gap -->
                            <ColumnDefinition Width="3*"/>
                            <ColumnDefinition Width="10"/>
                            <!-- Gap -->
                            <ColumnDefinition Width="4*"/>
                        </Grid.ColumnDefinitions>
                        <!-- Daily Idle Time -->
                        <Border Grid.Column="0" BorderThickness="2" CornerRadius="8" Padding="0,0,0,10">
                            <Border.BorderBrush>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                    <GradientStop Color="#AA60C8" Offset="0" />
                                    <GradientStop Color="#D69ADE" Offset="0.25" />
                                    <GradientStop Color="#EABDE6" Offset="0.5" />
                                    <GradientStop Color="#FFDFEF" Offset="0.75" />
                                </LinearGradientBrush>
                            </Border.BorderBrush>
                            <Grid Margin="10,0,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="10"/>
                                    <!-- Gap -->
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <Grid Grid.Column="0">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="20"/>
                                        <!-- Gap -->
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="20"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>

                                    <TextBlock Grid.Row="0" Text="Thời gian dừng hàng ngày" FontSize="20" FontWeight="Bold" Foreground="{StaticResource SurfaceBrush}" VerticalAlignment="Top" HorizontalAlignment="Left"/>

                                    <Grid Grid.Row="2">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="10"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Rectangle Width="10" Height="120" Fill="{StaticResource LoopGradientBrush2}"/>
                                        <TextBlock Grid.Column="2" Text="Đã dừng" FontSize="16" FontWeight="Bold" Foreground="{StaticResource SurfaceBrush}" Margin="0,0,10,0" VerticalAlignment="Top" HorizontalAlignment="Left"/>
                                        <TextBlock Grid.Column="2" Text="{Binding TodayTotalIdleTime}" FontSize="30" FontWeight="Bold" Foreground="#E74C3C" VerticalAlignment="Bottom" HorizontalAlignment="Left"/>
                                    </Grid>

                                    <Grid Grid.Row="4">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="10"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Rectangle Width="10" Height="120" Fill="{StaticResource LoopGradientBrush1}"/>
                                        <TextBlock Grid.Column="2" Text="Còn lại" FontSize="16" FontWeight="Bold" Foreground="{StaticResource SurfaceBrush}" Margin="0,0,10,0" VerticalAlignment="Top" HorizontalAlignment="Left"/>
                                        <TextBlock Grid.Column="2" Text="{Binding TodayRemainingIdleTime}" FontSize="30" FontWeight="Bold" Foreground="#348F50" VerticalAlignment="Bottom" HorizontalAlignment="Left"/>
                                    </Grid>

                                </Grid>

                                <Grid Grid.Column="2">
                                    <Border Grid.Row="0" BorderThickness="2" CornerRadius="5">
                                        <Grid>
                                            <lvc:PieChart x:Name="PieChart"
                                                      Series="{Binding Series}"
                                                      InitialRotation="-225"
                                                      MaxAngle="270"
                                                      LegendPosition="Bottom">
                                            </lvc:PieChart>
                                        </Grid>
                                    </Border>
                                </Grid>


                            </Grid>
                        </Border>
                        <!-- Monthly Idle Time -->
                        <Border Grid.Column="2" BorderThickness="2" CornerRadius="8" Padding="0,0,0,10">
                            <Border.BorderBrush>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                    <GradientStop Color="#EB5A3C" Offset="0" />
                                    <GradientStop Color="#DF9755" Offset="0.25" />
                                    <GradientStop Color="#E7D283" Offset="0.5" />
                                    <GradientStop Color="#EDF4C2" Offset="0.75" />
                                </LinearGradientBrush>
                            </Border.BorderBrush>
                            <Grid Margin="10,0,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="10"/>
                                    <!-- Gap -->
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <Grid Grid.Column="0">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="20"/>
                                        <!-- Gap -->
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="20"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>

                                    <TextBlock Grid.Row="0" Width="auto" Text="Thời gian dừng hàng tháng" FontSize="16" FontWeight="Bold" Foreground="{StaticResource SurfaceBrush}" VerticalAlignment="Top" HorizontalAlignment="Left"/>

                                    <Grid Grid.Row="2">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="20"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Rectangle Width="10" Height="120" Fill="{StaticResource LoopGradientBrush2}"/>
                                        <TextBlock Grid.Column="2" Text="Đã dừng" FontSize="16" FontWeight="Bold" Foreground="{StaticResource SurfaceBrush}" Margin="0,0,10,0" VerticalAlignment="Top" HorizontalAlignment="Left"/>
                                        <TextBlock Grid.Column="2" Text="{Binding MonthTotalIdleTime}" FontSize="30" FontWeight="Bold" Foreground="#E74C3C" VerticalAlignment="Bottom" HorizontalAlignment="Left"/>
                                    </Grid>

                                    <Grid Grid.Row="4">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="20"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Rectangle x:Name="SnakeRect" Width="10" Height="120" Fill="{StaticResource LoopGradientBrush1}"/>
                                        <TextBlock Grid.Column="2" Text="Còn lại" FontSize="16" FontWeight="Bold" Foreground="{StaticResource SurfaceBrush}" Margin="0,0,10,0" VerticalAlignment="Top" HorizontalAlignment="Left"/>
                                        <TextBlock Grid.Column="2" Text="{Binding MonthRemainingIdleTime}" FontSize="30" FontWeight="Bold" Foreground="#348F50" VerticalAlignment="Bottom" HorizontalAlignment="Left"/>
                                    </Grid>

                                </Grid>

                                <Grid Grid.Column="2">
                                    <Border Grid.Row="0" BorderThickness="2" CornerRadius="5">
                                        <Grid>
                                            <lvc:PieChart x:Name="PieChart2"
                                                      Series="{Binding Series}"
                                                      InitialRotation="-225"
                                                      MaxAngle="270"
                                                      LegendPosition="Bottom">
                                            </lvc:PieChart>
                                        </Grid>
                                    </Border>
                                </Grid>
                            </Grid>
                        </Border>
                        <!-- System status -->
                        <Border Grid.Column="4" BorderThickness="2" CornerRadius="8" Padding="0,0,0,10">
                            <Border.BorderBrush>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                    <GradientStop Color="#FF8595EF" Offset="0" />
                                    <GradientStop Color="#FF58FDDB" Offset="0.25" />
                                    <GradientStop Color="#FFFDD644" Offset="0.5" />
                                    <GradientStop Color="#FFA4D7F7" Offset="0.75" />
                                </LinearGradientBrush>
                            </Border.BorderBrush>
                            <StackPanel VerticalAlignment="Center">
                                <!-- Trạng thái chạy/dừng hệ thống -->
                                <TextBlock FontSize="30" Margin="10,0,0,0" Foreground="{StaticResource SurfaceBrush}" HorizontalAlignment="Center">
                                    <Run Text="Trạng thái: "/>
                                    <Run x:Name="SystemStatusRun" Text="Không kết nối" Foreground="#FF6B6B"/>
                                </TextBlock>

                                <!-- Tổng sản phẩm OK -->
                                <TextBlock FontSize="30" VerticalAlignment="Center" Margin="10,0,0,0" Foreground="{StaticResource SurfaceBrush}" HorizontalAlignment="Center">
                                    <Run Text="OK: "/>
                                    <Run x:Name="SystemTotalOKRun" Text="--" Foreground="#4ECDC4"/>
                                </TextBlock>

                                <!-- Tổng sản phẩm NG -->
                                <TextBlock FontSize="30" VerticalAlignment="Center" Margin="10,0,0,0" Foreground="{StaticResource SurfaceBrush}" HorizontalAlignment="Center">
                                    <Run Text="NG: "/>
                                    <Run x:Name="SystemTotalNGRun" Text="--" Foreground="#FF6B6B"/>
                                </TextBlock>
                            </StackPanel>
                        </Border>
                    </Grid>

                    <!-- Bottom Area: Daily Plan + Div3/Div4 -->
                    <Grid Grid.Row="2" Margin="10,10,10,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="3*"/>
                            <!-- 3/10 -->
                            <ColumnDefinition Width="5"/>
                            <!-- Gap -->
                            <ColumnDefinition Width="3*"/>
                            <!-- 3/10 -->
                            <ColumnDefinition Width="5"/>
                            <!-- Gap -->
                            <ColumnDefinition Width="4*"/>
                            <!-- 4/10 -->
                        </Grid.ColumnDefinitions>

                        <!-- Div3 và Div4 Section -->
                        <Grid Grid.Column="0">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="5"/>
                                <!-- Gap -->
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- Panel 3 (div3) - Left: Daily Plan vs Actual -->
                            <Border Grid.Row="0" BorderBrush="#00B4D8" BorderThickness="2" CornerRadius="8">
                                <local:DailyPlanActualChartControl HorizontalAlignment="Stretch" VerticalAlignment="Stretch"/>
                            </Border>

                            <!-- Panel 4 (div4) - Shift Plan vs Actual -->
                            <Border Grid.Row="2" BorderBrush="#2C98A0" BorderThickness="2" CornerRadius="8">
                                <local:ShiftPlanActualChartControl HorizontalAlignment="Stretch" VerticalAlignment="Stretch"/>
                            </Border>
                        </Grid>
                        <Grid Grid.Column="2">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="5"/>
                                <!-- Gap -->
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- Panel 3 (div3) - Right: Monthly Idle Hours -->
                            <Border Grid.Row="0" BorderBrush="#FFAFCC" BorderThickness="2" CornerRadius="8">
                                <local:DailyQualityChartControl VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            </Border>

                            <!-- Panel 4 (div4) - Shift Quality Chart -->
                            <Border Grid.Row="2" BorderBrush="#FFC8D0" BorderThickness="2" CornerRadius="8">
                                <local:ShiftQualityChartControl VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                            </Border>
                        </Grid>
                        <!-- Daily Plan Section -->
                        <Grid Grid.Column="4" x:Name="DailyPlanSection">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="5"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <Border CornerRadius="8" Grid.Column="0" BorderThickness="2" BorderBrush="#FA576B">
                                <ScrollViewer VerticalScrollBarVisibility="Auto">
                                    <Grid>
                                        <local:StopTimesChartControl 
                                            HorizontalAlignment="Stretch"
                                            VerticalAlignment="Stretch"/>
                                    </Grid>
                                </ScrollViewer>
                            </Border>
                            
                            <!-- Kế hoạch model hôm nay -->
                            <Border Padding="4,4,4,0" Grid.Row="2" CornerRadius="8" BorderThickness="2" BorderBrush="#CAF0F8">
                                <Grid Grid.Row="2" Margin="0,0,0,10">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>

                                    <Grid Grid.Row="0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <!-- Title -->
                                        <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                            <Path Data="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"
                                                  Fill="#3498DB" Width="auto" Height="auto" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="Kế hoạch" FontSize="16" FontWeight="Bold" Foreground="{StaticResource SurfaceBrush}" Margin="0,0,10,0"/>
                                            <TextBlock Text="{Binding TodayDateText}" FontSize="16" Foreground="{StaticResource SurfaceBrush}" VerticalAlignment="Center"/>
                                        </StackPanel>

                                        <!-- Current Product Display -->
                                        <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                            <TextBlock VerticalAlignment="Center" Text="Model hiện tại" FontSize="14" FontWeight="Bold" Foreground="{StaticResource SurfaceBrush}" Margin="0,0,10,0"/>

                                            <!-- Read-only display when not editing -->
                                            <Border Background="#ECF0F1" CornerRadius="4" Visibility="{Binding IsEditingModel, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="{Binding CurrentProductText}" FontSize="16" FontWeight="Bold" Foreground="#E74C3C" VerticalAlignment="Center"/>
                                                    <Button Margin="8,0,0,0" Width="20" Height="20" Background="Transparent" BorderThickness="0" Foreground="Black"
                                                            Command="{Binding StartEditModelCommand}" ToolTip="Chỉnh sửa model">
                                                        <Path Data="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"
                                                              Fill="#3498DB" Stretch="Uniform" Width="12" Height="12"/>
                                                    </Button>
                                                </StackPanel>
                                            </Border>

                                            <!-- Editable input when editing -->
                                            <StackPanel Orientation="Horizontal" Visibility="{Binding IsEditingModel, Converter={StaticResource BooleanToVisibilityConverter}}">
                                                <TextBox Text="{Binding EditableModelName, UpdateSourceTrigger=PropertyChanged}"
                                                         FontSize="16" FontWeight="Bold" Foreground="#E74C3C"
                                                         Background="#ECF0F1" BorderThickness="1" BorderBrush="#3498DB"
                                                         Padding="8,4" MinWidth="150" VerticalAlignment="Center">
                                                    <TextBox.InputBindings>
                                                        <KeyBinding Key="Enter" Command="{Binding SaveModelCommand}"/>
                                                        <KeyBinding Key="Escape" Command="{Binding CancelEditModelCommand}"/>
                                                    </TextBox.InputBindings>
                                                </TextBox>
                                                <Button Margin="4,0,0,0" Width="24" Height="24" Background="#27AE60" BorderThickness="0"
                                                        Command="{Binding SaveModelCommand}" ToolTip="Lưu (Enter)">
                                                    <Path Data="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"
                                                          Fill="White" Stretch="Uniform" Width="12" Height="12"/>
                                                </Button>
                                                <Button Margin="4,0,0,0" Width="24" Height="24" Background="#E74C3C" BorderThickness="0"
                                                        Command="{Binding CancelEditModelCommand}" ToolTip="Hủy (Escape)">
                                                    <Path Data="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"
                                                          Fill="White" Stretch="Uniform" Width="12" Height="12"/>
                                                </Button>
                                            </StackPanel>
                                        </StackPanel>

                                        <!-- Control Buttons -->
                                        <Button Grid.Column="2" x:Name="RefreshPlanButton" Width="35" Height="35" Margin="5,0"
                                                Background="#3498DB" BorderThickness="0" ToolTip="Cập nhật kế hoạch"
                                                Style="{StaticResource RoundButtonStyle}"
                                                Command="{Binding RefreshCommand}"
                                                Click="RefreshPlanButton_Click">
                                            <Path Data="M17.65,6.35C16.2,4.9 14.21,4 12,4c-4.42,0 -7.99,3.58 -7.99,8s3.57,8 7.99,8c3.73,0 6.84,-2.55 7.73,-6h-2.08c-0.82,2.33 -3.04,4 -5.65,4 -3.31,0 -6,-2.69 -6,-6s2.69,-6 6,-6c1.66,0 3.14,0.69 4.22,1.78L13,11h7V4L17.65,6.35z"
                                                  Fill="White" Stretch="Uniform" Width="18" Height="18"/>
                                        </Button>

                                        <Button Grid.Column="3" x:Name="MarkCompleteButton" Width="35" Height="35" Margin="5,0"
                                                Background="#27AE60" BorderThickness="0" ToolTip="Hoàn thành sản phẩm hiện tại"
                                                Style="{StaticResource RoundButtonStyle}"
                                                Command="{Binding MarkCompleteCommand}"
                                                Click="MarkCompleteButton_Click">
                                            <Path Data="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"
                                                  Fill="White" Stretch="Uniform" Width="18" Height="18"/>
                                        </Button>

                                        <Button Grid.Column="4" x:Name="StartNextButton" Width="35" Height="35" Margin="5,0"
                                                Background="#F39C12" BorderThickness="0" ToolTip="Bắt đầu sản phẩm được chọn"
                                                Style="{StaticResource RoundButtonStyle}"
                                                Command="{Binding StartNextCommand}"
                                                Click="StartNextButton_Click">
                                            <Path Data="M8,5.14V19.14L19,12.14L8,5.14Z"
                                                  Fill="White" Stretch="Uniform" Width="18" Height="18"/>
                                        </Button>
                                        <!-- DataGrid for Daily Plan -->
                                    </Grid>
                                    <Border Grid.Row="1" BorderBrush="Gray" BorderThickness="1" CornerRadius="10" Padding="2" Background="{StaticResource BorderBrush}">
                                        <DataGrid x:Name="DailyPlanDataGrid"
                                                  ColumnHeaderStyle="{StaticResource CenterHeaderStyle}"
                                                  ItemsSource="{Binding DailyPlanItems}"
                                                  SelectedItem="{Binding SelectedItem, Mode=TwoWay}"
                                                  AutoGenerateColumns="False"
                                                  CanUserAddRows="False"
                                                  CanUserDeleteRows="False"
                                                  CanUserReorderColumns="False"
                                                  CanUserResizeRows="False"
                                                  IsReadOnly="True"
                                                  SelectionMode="Single"
                                                  GridLinesVisibility="All"
                                                  HeadersVisibility="Column"
                                                  RowHeaderWidth="0"
                                                  FontSize="14"
                                                  MinHeight="200"
                                                  HorizontalScrollBarVisibility="Auto"
                                                  VerticalScrollBarVisibility="Auto"
                                                  BorderThickness="0"
                                                  Background="{x:Null}">

                                            <DataGrid.RowStyle>
                                                <Style TargetType="DataGridRow">
                                                    <Setter Property="Background" Value="{Binding Status, Converter={StaticResource StatusToColorConverter}}"/>
                                                    <Setter Property="Foreground" Value="{Binding Status, Converter={StaticResource StatusToTextColorConverter}}"/>
                                                    <Setter Property="Height" Value="40"/>
                                                    <Style.Triggers>
                                                        <!-- Prevent selection for completed and in-progress items -->
                                                        <DataTrigger Binding="{Binding Status}" Value="Completed">
                                                            <Setter Property="IsEnabled" Value="False"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="InProgress">
                                                            <Setter Property="IsEnabled" Value="False"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </DataGrid.RowStyle>

                                            <DataGrid.Columns>
                                                <DataGridTextColumn ElementStyle="{StaticResource CenterTextBlockStyle}" Header="No" Binding="{Binding No}" Width="*" MinWidth="40"/>
                                                <DataGridTextColumn ElementStyle="{StaticResource CenterTextBlockStyle}" Header="Type" Binding="{Binding Type}" Width="*" MinWidth="60"/>
                                                <DataGridTextColumn ElementStyle="{StaticResource CenterTextBlockStyle}" Header="Model name" Binding="{Binding ModelName}" Width="*" MinWidth="120"/>
                                                <DataGridTextColumn ElementStyle="{StaticResource CenterTextBlockStyle}" Header="Market" Binding="{Binding Market}" Width="*" MinWidth="80"/>
                                                <DataGridTextColumn ElementStyle="{StaticResource CenterTextBlockStyle}" Header="Q'ty" Binding="{Binding Quantity}" Width="*" MinWidth="50"/>
                                                <DataGridTextColumn ElementStyle="{StaticResource CenterTextBlockStyle}" Header="Start time" Binding="{Binding StartTime}" Width="*" MinWidth="80"/>
                                                <DataGridTextColumn ElementStyle="{StaticResource CenterTextBlockStyle}" Header="Stop time" Binding="{Binding StopTime}" Width="*" MinWidth="80"/>
                                            </DataGrid.Columns>
                                        </DataGrid>
                                    </Border>

                                </Grid>
                            </Border>
                        </Grid>
                    </Grid>
                </Grid>

                <!-- Trang Kế hoạch -->
                <ContentControl x:Name="PlanPage" Visibility="Collapsed">
                    <views:PlanPage />
                </ContentControl>

                <!-- Trang Báo cáo -->
                <ContentControl x:Name="ReportPage" Visibility="Collapsed">
                    <views:ReportPage />
                </ContentControl>

                <!-- Trang Bảo dưỡng -->
                <ContentControl x:Name="MaintenancePage" Visibility="Collapsed">
                    <views:MaintenancePage />
                </ContentControl>

                <!-- Trang Cài đặt -->
                <ContentControl x:Name="SettingsPage" Visibility="Collapsed">
                    <views:SettingsPage />
                </ContentControl>

                <!-- Trang Người dùng -->
                <ContentControl x:Name="UsersPage" Visibility="Collapsed">
                    <views:UsersPage />
                </ContentControl>
            </Grid>

            <!-- Error List Popup -->
            <Popup x:Name="ErrorListPopup" Placement="Bottom" PlacementTarget="{Binding ElementName=ErrorBellButton}"
               StaysOpen="False" AllowsTransparency="True" PopupAnimation="Slide"
               IsOpen="{Binding IsErrorListPopupOpen, ElementName=mainWindow, Mode=TwoWay}">
                <Border Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="1" CornerRadius="3"
                    Padding="10" MaxHeight="300" MinWidth="350" MaxWidth="500">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <TextBlock Text="Error Notifications" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                        <ListBox Grid.Row="1" x:Name="ErrorListBox" ItemsSource="{Binding AllErrors, ElementName=mainWindow}"
                             ScrollViewer.VerticalScrollBarVisibility="Auto">
                            <ListBox.ItemContainerStyle>
                                <Style TargetType="ListBoxItem">
                                    <Setter Property="Padding" Value="3"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsNew}" Value="True">
                                            <Setter Property="FontWeight" Value="Bold"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </ListBox.ItemContainerStyle>
                        </ListBox>
                        <Button Grid.Row="2" Content="Clear All Errors (Display Only)" Click="ClearAllErrorsButton_Click" Margin="0,10,0,0" HorizontalAlignment="Right" Padding="5,2"/>
                    </Grid>
                </Border>
            </Popup>
        </Grid>
    </Grid>
</Window>
