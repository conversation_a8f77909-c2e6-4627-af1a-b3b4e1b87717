﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Collections.ObjectModel;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using ZoomableApp.Models;
using ZoomableApp.SharedControls;

namespace ZoomableApp.Layouts
{
    /// <summary>
    /// Interaction logic for MainlineLayout.xaml
    /// </summary>
    public partial class MainlineLayout : UserControl
    {
        public event EventHandler<ProductItemEventArgs> ProductExitingLine;

        private List<StationControl> _stations = new List<StationControl>();
        // Product data (model)
        private ObservableCollection<ProductItem> _activeProductsData = new ObservableCollection<ProductItem>();
        // Visual representation of products, keyed by ProductItem.ProductId
        private Dictionary<string, ProductVisualControl> _productVisuals = new Dictionary<string, ProductVisualControl>();

        private DispatcherTimer _simulationTimer;
        private DispatcherTimer _productEntryTimer; // For adding new products for demo
        private int _productCounter = 0;

        private List<double> _stationCenterXVals = new List<double>();

        public MainlineLayout()
        {
            InitializeComponent();
            Loaded += MainlineLayout_Loaded;
            Unloaded += MainlineLayout_Unloaded;
        }

        private void MainlineLayout_Loaded(object sender, RoutedEventArgs e)
        {
            InitializeStations(); // Populate StationStackPanel
            CalculateStationPositions(); // Calculate X-coordinates for product placement

            // Timer to simulate "Done" signal and move products
            _simulationTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(5) // "Done" signal every 5s
            };
            _simulationTimer.Tick += SimulationTimer_Tick;
            _simulationTimer.Start();

            // Timer to add new products for demonstration
            _productEntryTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(7) // Add a product every 7s
            };
            _productEntryTimer.Tick += ProductEntryTimer_Tick;
            _productEntryTimer.Start();
        }

        private void MainlineLayout_Unloaded(object sender, RoutedEventArgs e)
        {
            _simulationTimer?.Stop();
            _productEntryTimer?.Stop();
            // Clean up visuals from canvas if necessary, though they should be gone with product removal
            ProductLayerCanvas.Children.Clear();
            _productVisuals.Clear();
            _activeProductsData.Clear();
        }

        private void InitializeStations()
        {
            StationStackPanel.Children.Clear();
            _stations.Clear();

            // Example: Create 10 stations for demo
            for (int i = 1; i <= 26; i++)
            {
                var station = new StationControl
                {
                    StationNumber = i.ToString("00"),
                    StationLength = 120, // Example length
                    // Assign types for variety
                    StationType = (i % 3 == 0) ? StationType.TestRoller :
                                  (i % 3 == 1) ? StationType.WorkerRoller : StationType.StandardRoller,
                    DeviceStatus = DeviceOperationalStatus.Idle
                };
                _stations.Add(station);
                StationStackPanel.Children.Add(station);
            }
            // Ensure layout is updated to get ActualWidth for stations
            UpdateLayout();
        }

        private void CalculateStationPositions()
        {
            _stationCenterXVals.Clear();
            if (_stations.Count == 0 || _stations[0].ActualWidth == 0)
            {
                // This can happen if called before layout pass.
                // Force a layout pass if needed, or call this after stations are fully rendered.
                StationStackPanel.UpdateLayout(); // Try to force it
            }

            double currentXOffset = 0;
            foreach (var station in _stations)
            {
                if (station.ActualWidth == 0)
                {
                    // Fallback or log warning if ActualWidth is still 0
                    // For robustness, you might need to subscribe to SizeChanged for StationStackPanel
                    // or individual stations if their sizes can change dynamically after initial load.
                    // For now, assume they are fixed after InitializeStations and UpdateLayout.
                    // A common width if not available:
                    _stationCenterXVals.Add(currentXOffset + (station.StationLength / 2));
                    currentXOffset += station.StationLength;

                }
                else
                {
                    _stationCenterXVals.Add(currentXOffset + station.ActualWidth / 2);
                    currentXOffset += station.ActualWidth;
                }
            }
        }

        private void ProductEntryTimer_Tick(object sender, EventArgs e)
        {
            // Add a new product if the first station is available
            if (_stations.Any() && !_stations[0].HasProduct)
            {
                _productCounter++;
                var newProductData = new ProductItem($"PID{_productCounter}", $"PROD-{_productCounter:D3}")
                {
                    CurrentStationIndex = -1 // Not yet on a station
                };
                // Simulate test result for demo if first station is test
                if (_stations[0].IsTestStationType)
                {
                    newProductData.CurrentTestResult = TestResultStatus.Testing; // Or AwaitingProduct
                }

                _activeProductsData.Add(newProductData);
                AddProductVisualToLine(newProductData);
            }
        }

        private void AddProductVisualToLine(ProductItem productData)
        {
            var productVisual = new ProductVisualControl { DataContext = productData };
            _productVisuals[productData.ProductId] = productVisual;
            ProductLayerCanvas.Children.Add(productVisual);

            // Initial placement (e.g., at the center of the first station)
            productData.CurrentStationIndex = 0;
            var targetStation = _stations[0];
            targetStation.HasProduct = true;
            if (targetStation.IsTestStationType)
            {
                targetStation.ProductStatusAtStation = productData.CurrentTestResult;
            }
            targetStation.UpdateStationDisplayBrush();


            // Ensure visual is measured to get DesiredSize
            productVisual.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
            double visualWidth = productVisual.DesiredSize.Width;
            double visualHeight = productVisual.DesiredSize.Height;

            double targetX = _stationCenterXVals[0] - (visualWidth / 2);
            double targetY = (ProductLayerCanvas.ActualHeight - visualHeight) / 2; // Center vertically

            Canvas.SetLeft(productVisual, targetX);
            Canvas.SetTop(productVisual, targetY);
        }


        private void SimulationTimer_Tick(object sender, EventArgs e)
        {
            if (_stationCenterXVals.Count == 0 && _stations.Count > 0)
            {
                CalculateStationPositions(); // Recalculate if somehow missed
                if (_stationCenterXVals.Count == 0) return; // Still no positions, cannot proceed
            }

            // SYNCHRONIZED MOVEMENT: All products move together like a conveyor belt
            // First, collect all movement operations to perform them simultaneously
            var movementOperations = new List<MovementOperation>();

            // Process products from the end of the line to the beginning to avoid collisions in logic
            for (int i = _activeProductsData.Count - 1; i >= 0; i--)
            {
                var productData = _activeProductsData[i];
                var productVisual = _productVisuals[productData.ProductId];
                int currentStationIdx = productData.CurrentStationIndex;

                if (currentStationIdx < 0) continue; // Product not on line or already processed to exit

                StationControl currentStation = _stations[currentStationIdx];

                // Simulate "work done" - for test stations, assign a result
                if (currentStation.IsTestStationType && productData.CurrentTestResult == TestResultStatus.Testing)
                {
                    // Simple random OK/NG for demo
                    productData.CurrentTestResult = (new Random().Next(0, 10) < 8) ? TestResultStatus.OK : TestResultStatus.NG;
                    currentStation.ProductStatusAtStation = productData.CurrentTestResult; // Update station's view of product
                    currentStation.UpdateStationDisplayBrush(); // Re-color station based on test result
                }

                int nextStationIdx = currentStationIdx + 1;

                if (nextStationIdx < _stations.Count) // If there's a next station
                {
                    // Plan movement to next station (will be executed if all movements are valid)
                    movementOperations.Add(new MovementOperation
                    {
                        ProductData = productData,
                        ProductVisual = productVisual,
                        FromStationIndex = currentStationIdx,
                        ToStationIndex = nextStationIdx,
                        IsExitingLine = false
                    });
                }
                else // Product is at the last station and will move off the line
                {
                    // Plan movement off the line
                    movementOperations.Add(new MovementOperation
                    {
                        ProductData = productData,
                        ProductVisual = productVisual,
                        FromStationIndex = currentStationIdx,
                        ToStationIndex = -1, // Off the line
                        IsExitingLine = true
                    });
                }
            }

            // Check if all movements are valid (no conflicts)
            if (AreAllMovementsValid(movementOperations))
            {
                // Execute all movements simultaneously
                ExecuteAllMovements(movementOperations);
            }
            // If movements are not valid, wait for next cycle (products stay in place)
        }

        private void AnimateProductToStation(ProductVisualControl visual, int stationIndex)
        {
            if (stationIndex < 0 || stationIndex >= _stationCenterXVals.Count) return;

            double visualWidth = visual.DesiredSize.Width; // Assuming it's already measured
            if (visualWidth == 0)
            { // ensure it has a size
                visual.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
                visualWidth = visual.DesiredSize.Width;
            }

            double targetX = _stationCenterXVals[stationIndex] - (visualWidth / 2);

            var animation = new DoubleAnimation
            {
                To = targetX,
                Duration = TimeSpan.FromSeconds(0.8), // Animation duration
                EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseInOut }
            };

            Storyboard.SetTarget(animation, visual);
            Storyboard.SetTargetProperty(animation, new PropertyPath(Canvas.LeftProperty));

            var sb = new Storyboard();
            sb.Children.Add(animation);
            sb.Begin();
        }

        private void AnimateProductOffLine(ProductVisualControl visual, ProductItem productData)
        {
            double targetX = ProductLayerCanvas.ActualWidth + 50; // Move it off to the right

            var animation = new DoubleAnimation
            {
                To = targetX,
                Duration = TimeSpan.FromSeconds(0.8),
                EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseInOut }
            };

            Storyboard.SetTarget(animation, visual);
            Storyboard.SetTargetProperty(animation, new PropertyPath(Canvas.LeftProperty));

            var sb = new Storyboard();
            sb.Children.Add(animation);
            sb.Completed += (s, e) =>
            {
                ProductLayerCanvas.Children.Remove(visual);
                // Keep productData in _activeProductsData and _productVisuals for a moment
                // The receiver (MainWindow) will decide if it truly gets removed from mainline's tracking
                // Or, mainline always removes, and just passes a copy of the data.
                // For simplicity now, Mainline will remove from its internal lists after firing event.
                if (productData != null)
                {
                    ProductExitingLine?.Invoke(this, new ProductItemEventArgs(productData)); // Raise event FIRST

                    _productVisuals.Remove(productData.ProductId); // Then remove
                    _activeProductsData.Remove(productData);
                }
                System.Diagnostics.Debug.WriteLine($"Product {productData?.ProductId} exited Mainline.");
            };
            sb.Begin();
        }

        // Helper class for synchronized movement operations
        private class MovementOperation
        {
            public ProductItem ProductData { get; set; }
            public ProductVisualControl ProductVisual { get; set; }
            public int FromStationIndex { get; set; }
            public int ToStationIndex { get; set; }
            public bool IsExitingLine { get; set; }
        }

        // Check if all planned movements are valid (no station conflicts)
        private bool AreAllMovementsValid(List<MovementOperation> operations)
        {
            // Create a set of destination stations to check for conflicts
            var destinationStations = new HashSet<int>();

            foreach (var operation in operations)
            {
                if (!operation.IsExitingLine)
                {
                    // Check if destination station is already occupied or targeted by another product
                    if (destinationStations.Contains(operation.ToStationIndex))
                    {
                        return false; // Conflict: two products trying to move to same station
                    }

                    // Check if destination station is currently occupied by a product not moving
                    var destinationStation = _stations[operation.ToStationIndex];
                    if (destinationStation.HasProduct)
                    {
                        // Check if the product at destination is also moving
                        bool destinationProductIsMoving = operations.Any(op =>
                            op.FromStationIndex == operation.ToStationIndex);

                        if (!destinationProductIsMoving)
                        {
                            return false; // Destination is occupied and product there is not moving
                        }
                    }

                    destinationStations.Add(operation.ToStationIndex);
                }
            }

            return true; // All movements are valid
        }

        // Execute all movement operations simultaneously
        private void ExecuteAllMovements(List<MovementOperation> operations)
        {
            // First, update all station occupancy states
            foreach (var operation in operations)
            {
                // Vacate source station
                var fromStation = _stations[operation.FromStationIndex];
                fromStation.HasProduct = false;
                if (fromStation.IsTestStationType)
                    fromStation.ProductStatusAtStation = TestResultStatus.None;
                fromStation.UpdateStationDisplayBrush();

                if (!operation.IsExitingLine)
                {
                    // Occupy destination station
                    var toStation = _stations[operation.ToStationIndex];
                    operation.ProductData.CurrentStationIndex = operation.ToStationIndex;
                    toStation.HasProduct = true;

                    if (toStation.IsTestStationType)
                    {
                        // Product arrives at a test station, set its status for the station
                        operation.ProductData.CurrentTestResult = TestResultStatus.Testing; // Reset for new test
                        toStation.ProductStatusAtStation = operation.ProductData.CurrentTestResult;
                    }
                    toStation.UpdateStationDisplayBrush();
                }
            }

            // Then, animate all products simultaneously
            foreach (var operation in operations)
            {
                if (operation.IsExitingLine)
                {
                    AnimateProductOffLine(operation.ProductVisual, operation.ProductData);
                }
                else
                {
                    AnimateProductToStation(operation.ProductVisual, operation.ToStationIndex);
                }
            }
        }
    }
}
