﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ZoomableApp.Models
{
    public enum PlcDeviceAddress
    {
        BitTest,
        ProductCodeAtReader,
        // ---- Trạng thái chung ----
        SystemReadyBit,         // Bit báo hệ thống PLC sẵn sàng
        SystemErrorBit,         // Bit báo lỗi hệ thống chung
        EmergencyStopBit,       // Bit báo dừng khẩn cấp

        // ---- <PERSON>h ghi phát hiện vị trí có hàng (cho từng trạm) ----
        // <PERSON><PERSON><PERSON> sử có tối đa 30 trạm, mỗi trạm 1 bit
        Station01_ProductPresenceBit,
        Station02_ProductPresenceBit,
        // ... (tương tự cho các trạm khác)
        Station30_ProductPresenceBit,

        // ---- Thanh ghi trạng thái test OK/NG (cho từng trạm test) ----
        // <PERSON><PERSON><PERSON> sử các trạm test có thanh ghi riêng để lưu kết quả (0=None, 1=OK, 2=NG)
        TestStation09_ResultWord, // Trạm 09
        TestStation10_ResultWord, // Trạm 10
        // ... (tương tự cho các trạm test khác)
        TestStation15_ResultWord,

        // ---- Thanh ghi thời gian thao tác (cho từng trạm công nhân) ----
        // Lưu dưới dạng WORD (ví dụ: số giây)
        WorkerStation03_OperationTimeWord,
        WorkerStation04_OperationTimeWord,
        // ... (tương tự cho các trạm công nhân khác)
        WorkerStation08_OperationTimeWord,

        // ---- Thanh ghi trạng thái thiết bị (cho từng trạm/thiết bị quan trọng) ----
        // (0=None, 1=Idle, 2=Running, 3=Error, 4=Off, 5=Maintenance)
        Device_Lifter_StatusWord,
        Device_Crane_StatusWord,
        Device_TestMachineVoltage1_StatusWord,
        Device_TestMachineCurrentLeak_StatusWord,
        // ... (có thể có nhiều thanh ghi trạng thái thiết bị hơn)

        // ---- Thanh ghi lưu mã sản phẩm (cho các vị trí đọc mã/test) ----
        // Giả sử mã sản phẩm là string, lưu trong nhiều WORD liên tiếp.
        // Chúng ta sẽ cần địa chỉ bắt đầu và độ dài.
        ProductCode_ReaderStation_StartWord, // Vị trí đọc mã đầu vào
        ProductCode_TestStation09_StartWord, // Mã sản phẩm tại trạm test 09
        // ...

        // ---- Thanh ghi lưu số thứ tự sản phẩm ----
        // Có thể là một WORD hoặc DWORD
        CurrentProductSequenceNumberWord, // Số thứ tự của sản phẩm đang được xử lý chung
        TestStation09_ProductSequenceNumberWord, // Số thứ tự tại trạm test 09 (nếu cần riêng)

        // ---- Các thanh ghi điều khiển/phản hồi khác (ví dụ) ----
        ConveyorStartStopBit,       // Bit điều khiển băng chuyền
        ConveyorSpeedSetWord,       // Thanh ghi cài đặt tốc độ
        ConveyorActualSpeedWord,    // Thanh ghi tốc độ thực tế

        // ---- Thanh ghi cho báo cáo sản xuất (PLC_Test_Machine_1) ----
        // Sản lượng
        Product_OK,                 // D1000 - Số sản phẩm đạt chất lượng
        Product_NG,                 // D1002 - Số sản phẩm không đạt chất lượng
        Product_Total,              // D1004 - Tổng số sản phẩm

        // Thời gian hoàn thành thao tác (D72-D79 cho ST1-ST18)
        Time_Complete_ST1,          // D72
        Time_Complete_ST2,          // D73
        Time_Complete_ST3,          // D74
        Time_Complete_ST4,          // D75
        Time_Complete_ST5,          // D76
        Time_Complete_ST6,          // D77
        Time_Complete_ST7,          // D78
        Time_Complete_ST8,          // D79
        Time_Complete_ST9,          // D80
        Time_Complete_ST10,         // D81
        Time_Complete_ST11,         // D82
        Time_Complete_ST12,         // D83
        Time_Complete_ST13,         // D84
        Time_Complete_ST14,         // D85
        Time_Complete_ST15,         // D86
        Time_Complete_ST16,         // D87
        Time_Complete_ST17,         // D88
        Time_Complete_ST18,         // D89

        // Thời gian trễ (D1080-D1114 cho ST1-ST18)
        Time_Delay_ST1,             // D1080
        Time_Delay_ST2,             // D1082
        Time_Delay_ST3,             // D1084
        Time_Delay_ST4,             // D1086
        Time_Delay_ST5,             // D1088
        Time_Delay_ST6,             // D1090
        Time_Delay_ST7,             // D1092
        Time_Delay_ST8,             // D1094
        Time_Delay_ST9,             // D1096
        Time_Delay_ST10,            // D1098
        Time_Delay_ST11,            // D1100
        Time_Delay_ST12,            // D1102
        Time_Delay_ST13,            // D1104
        Time_Delay_ST14,            // D1106
        Time_Delay_ST15,            // D1108
        Time_Delay_ST16,            // D1110
        Time_Delay_ST17,            // D1112
        Time_Delay_ST18,            // D1114

        // Tổng thời gian dừng (D1132-D1182 cho ST1-ST18)
        Time_Stop_ST1,              // D1132
        Time_Stop_ST2,              // D1134
        Time_Stop_ST3,              // D1136
        Time_Stop_ST4,              // D1138
        Time_Stop_ST5,              // D1140
        Time_Stop_ST6,              // D1142
        Time_Stop_ST7,              // D1144
        Time_Stop_ST8,              // D1146
        Time_Stop_ST9,              // D1148
        Time_Stop_ST10,             // D1150
        Time_Stop_ST11,             // D1152
        Time_Stop_ST12,             // D1154
        Time_Stop_ST13,             // D1156
        Time_Stop_ST14,             // D1158
        Time_Stop_ST15,             // D1160
        Time_Stop_ST16,             // D1162
        Time_Stop_ST17,             // D1164
        Time_Stop_ST18,             // D1166

        // Số lần dừng (D1190-D1215 cho ST1-ST18)
        Number_Stop_ST1,            // D1190
        Number_Stop_ST2,            // D1191
        Number_Stop_ST3,            // D1192
        Number_Stop_ST4,            // D1193
        Number_Stop_ST5,            // D1194
        Number_Stop_ST6,            // D1195
        Number_Stop_ST7,            // D1196
        Number_Stop_ST8,            // D1197
        Number_Stop_ST9,            // D1198
        Number_Stop_ST10,           // D1199
        Number_Stop_ST11,           // D1200
        Number_Stop_ST12,           // D1201
        Number_Stop_ST13,           // D1202
        Number_Stop_ST14,           // D1203
        Number_Stop_ST15,           // D1204
        Number_Stop_ST16,           // D1205
        Number_Stop_ST17,           // D1206
        Number_Stop_ST18,           // D1207

        // Mã lỗi (M0, X06-X1610)
        Error_M0,                   // M0 - Lỗi chung
        Error_X06,                  // X06 - Lỗi input đầu vào
        Error_X1610,                // X1610 - Lỗi input cuối

        // ---- Trạng thái hệ thống cho Layout Controls ----
        SystemRunningStatus,        // Trạng thái chạy/dừng hệ thống (0=Stopped, 1=Running)
        SystemTotalOK,              // Tổng sản phẩm OK
        SystemTotalNG,              // Tổng sản phẩm NG

        // ---- Fault/Error Registers ----
        MainlineFaultCode,          // Mã lỗi hệ thống Mainline (string/number)
        InspectionFaultCode,        // Mã lỗi hệ thống Inspection (string/number)
    }

    public enum PlcDataType
    {
        BIT,
        WORD,       // 16-bit signed/unsigned
        DWORD,      // 32-bit signed/unsigned
        FLOAT,      // Single-precision float
        STRING      // Chuỗi ký tự, thường lưu trong nhiều WORD
    }
}
