using System;
using System.Collections.Generic;
using Microsoft.Data.Sqlite;
using System.Threading.Tasks;
using ZoomableApp.Models;
using ZoomableApp.PLC;
using DocumentFormat.OpenXml.Drawing;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Service xử lý dữ liệu sản xuất từ PLC và lưu vào database
    /// </summary>
    public class ProductionDataService
    {
        private readonly string _connectionString;
        private readonly PlcConnectionManager _plcManager;

        public ProductionDataService(PlcConnectionManager plcManager)
        {
            _plcManager = plcManager;
            _connectionString = "Data Source=Data/panaDB.db;";
        }

        /// <summary>
        /// Tạo bảng ProductionData nếu chưa tồn tại
        /// </summary>
        public async Task InitializeDatabaseAsync()
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var createTableQuery = @"
                    CREATE TABLE IF NOT EXISTS ProductionData (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Timestamp VARCHAR(255) NOT NULL,
                        Station VARCHAR(255) NOT NULL,
                        Product_OK INTEGER DEFAULT 0,
                        Product_NG INTEGER DEFAULT 0,
                        Product_Total INTEGER DEFAULT 0,
                        Time_Complete REAL DEFAULT 0,
                        Time_Delay INTEGER DEFAULT 0,
                        Time_Stop INTEGER DEFAULT 0,
                        Number_Stop INTEGER DEFAULT 0,
                        Error_Code VARCHAR(255) DEFAULT '',
                        Error_Text VARCHAR(255) DEFAULT '',
                        WorkShift VARCHAR(255) DEFAULT '',
                        ReportType VARCHAR(255) DEFAULT 'Production',
                        CreatedBy VARCHAR(255) DEFAULT '',
                        Notes VARCHAR(255) DEFAULT ''
                    );";

                using var command = new SqliteCommand(createTableQuery, connection);
                await command.ExecuteNonQueryAsync();

                System.Diagnostics.Debug.WriteLine("ProductionData table created successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating ProductionData table: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Đọc dữ liệu từ PLC và tạo ProductionData
        /// </summary>
        public async Task<ProductionData> ReadProductionDataFromPlcAsync(string station = "ALL")
        {
            try
            {
                var productionData = new ProductionData
                {
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    Station = station,
                    WorkShift = WorkShiftHelper.GetShiftName(WorkShiftHelper.GetCurrentShift()),
                    CreatedBy = "System"
                };

                // Đọc dữ liệu sản lượng từ PLC_Test_Machine_1
                if (_plcManager.IsPlcConnected("PLC_Test_Machine_1"))
                {
                    var plcService = _plcManager.GetPlcService("PLC_Test_Machine_1");
                    
                    // Đọc sản lượng
                    var productOkResult = await plcService.ReadAsync(PlcDeviceAddress.Product_OK);
                    if (productOkResult.IsSuccess)
                        productionData.Product_OK = Convert.ToInt32(productOkResult.Value);

                    var productNgResult = await plcService.ReadAsync(PlcDeviceAddress.Product_NG);
                    if (productNgResult.IsSuccess)
                        productionData.Product_NG = Convert.ToInt32(productNgResult.Value);

                    var productTotalResult = await plcService.ReadAsync(PlcDeviceAddress.Product_Total);
                    if (productTotalResult.IsSuccess)
                        productionData.Product_Total = Convert.ToInt32(productTotalResult.Value);

                    // Đọc thời gian hoàn thành (trung bình từ các trạm)
                    float totalCompleteTime = 0;
                    int validStations = 0;
                    
                    for (int i = 1; i <= 18; i++)
                    {
                        var stationEnum = (PlcDeviceAddress)Enum.Parse(typeof(PlcDeviceAddress), $"Time_Complete_ST{i}");
                        var timeResult = await plcService.ReadAsync(stationEnum);
                        if (timeResult.IsSuccess)
                        {
                            totalCompleteTime += Convert.ToSingle(timeResult.Value);
                            validStations++;
                        }
                    }
                    
                    if (validStations > 0)
                        productionData.Time_Complete = totalCompleteTime / validStations;

                    // Đọc thời gian trễ (tổng từ các trạm)
                    int totalDelayTime = 0;
                    for (int i = 1; i <= 18; i++)
                    {
                        var stationEnum = (PlcDeviceAddress)Enum.Parse(typeof(PlcDeviceAddress), $"Time_Delay_ST{i}");
                        var delayResult = await plcService.ReadAsync(stationEnum);
                        if (delayResult.IsSuccess)
                            totalDelayTime += Convert.ToInt32(delayResult.Value);
                    }
                    productionData.Time_Delay = totalDelayTime;

                    // Đọc thời gian dừng (tổng từ các trạm)
                    int totalStopTime = 0;
                    for (int i = 1; i <= 18; i++)
                    {
                        var stationEnum = (PlcDeviceAddress)Enum.Parse(typeof(PlcDeviceAddress), $"Time_Stop_ST{i}");
                        var stopResult = await plcService.ReadAsync(stationEnum);
                        if (stopResult.IsSuccess)
                            totalStopTime += Convert.ToInt32(stopResult.Value);
                    }
                    productionData.Time_Stop = totalStopTime;

                    // Đọc số lần dừng (tổng từ các trạm)
                    int totalStopCount = 0;
                    for (int i = 1; i <= 18; i++)
                    {
                        var stationEnum = (PlcDeviceAddress)Enum.Parse(typeof(PlcDeviceAddress), $"Number_Stop_ST{i}");
                        var stopCountResult = await plcService.ReadAsync(stationEnum);
                        if (stopCountResult.IsSuccess)
                            totalStopCount += Convert.ToInt32(stopCountResult.Value);
                    }
                    productionData.Number_Stop = totalStopCount;

                    // Đọc mã lỗi
                    var errorCodes = new List<string>();
                    
                    var errorM0Result = await plcService.ReadAsync(PlcDeviceAddress.Error_M0);
                    if (errorM0Result.IsSuccess && Convert.ToBoolean(errorM0Result.Value))
                        errorCodes.Add("M0");

                    var errorX06Result = await plcService.ReadAsync(PlcDeviceAddress.Error_X06);
                    if (errorX06Result.IsSuccess && Convert.ToBoolean(errorX06Result.Value))
                        errorCodes.Add("X06");

                    var errorX1610Result = await plcService.ReadAsync(PlcDeviceAddress.Error_X1610);
                    if (errorX1610Result.IsSuccess && Convert.ToBoolean(errorX1610Result.Value))
                        errorCodes.Add("X1610");

                    productionData.Error_Code = string.Join(", ", errorCodes);
                    productionData.Error_Text = GetErrorDescription(productionData.Error_Code);
                }

                return productionData;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error reading production data from PLC: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Lưu dữ liệu sản xuất vào database
        /// </summary>
        public async Task<bool> SaveProductionDataAsync(ProductionData data)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var insertQuery = @"
                    INSERT INTO ProductionData
                    (Timestamp, Station, Product_OK, Product_NG, Product_Total, Time_Complete,
                     Time_Delay, Time_Stop, Number_Stop, Error_Code, Error_Text, WorkShift,
                     ReportType, CreatedBy, Notes)
                    VALUES
                    (@Timestamp, @Station, @Product_OK, @Product_NG, @Product_Total, @Time_Complete,
                     @Time_Delay, @Time_Stop, @Number_Stop, @Error_Code, @Error_Text, @WorkShift,
                     @ReportType, @CreatedBy, @Notes)";

                using var command = new SqliteCommand(insertQuery, connection);
                command.Parameters.AddWithValue("@Timestamp", data.Timestamp);
                command.Parameters.AddWithValue("@Station", data.Station);
                command.Parameters.AddWithValue("@Product_OK", data.Product_OK);
                command.Parameters.AddWithValue("@Product_NG", data.Product_NG);
                command.Parameters.AddWithValue("@Product_Total", data.Product_Total);
                command.Parameters.AddWithValue("@Time_Complete", data.Time_Complete);
                command.Parameters.AddWithValue("@Time_Delay", data.Time_Delay);
                command.Parameters.AddWithValue("@Time_Stop", data.Time_Stop);
                command.Parameters.AddWithValue("@Number_Stop", data.Number_Stop);
                command.Parameters.AddWithValue("@Error_Code", data.Error_Code);
                command.Parameters.AddWithValue("@Error_Text", data.Error_Text);
                command.Parameters.AddWithValue("@WorkShift", data.WorkShift);
                command.Parameters.AddWithValue("@ReportType", data.ReportType);
                command.Parameters.AddWithValue("@CreatedBy", data.CreatedBy);
                command.Parameters.AddWithValue("@Notes", data.Notes);

                var result = await command.ExecuteNonQueryAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving production data: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Lấy mô tả lỗi từ mã lỗi
        /// </summary>
        private string GetErrorDescription(string errorCode)
        {
            if (string.IsNullOrEmpty(errorCode))
                return "";

            var descriptions = new Dictionary<string, string>
            {
                { "M0", "Lỗi hệ thống chung" },
                { "X06", "Lỗi input đầu vào" },
                { "X1610", "Lỗi input cuối" }
            };

            var codes = errorCode.Split(new[] { ", " }, StringSplitOptions.RemoveEmptyEntries);
            var result = new List<string>();

            foreach (var code in codes)
            {
                if (descriptions.ContainsKey(code.Trim()))
                    result.Add(descriptions[code.Trim()]);
            }

            return string.Join(", ", result);
        }

        /// <summary>
        /// Lấy dữ liệu sản xuất theo điều kiện
        /// </summary>
        public async Task<List<ProductionData>> GetProductionDataAsync(
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            string workShift = null, 
            string reportType = null)
        {
            var result = new List<ProductionData>();

            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var query = "SELECT * FROM ProductionData WHERE 1=1";
                var parameters = new List<SqliteParameter>();

                if (fromDate.HasValue)
                {
                    query += " AND date(Timestamp) >= date(@FromDate)";
                    parameters.Add(new SqliteParameter("@FromDate", fromDate.Value.ToString("yyyy-MM-dd")));
                }

                if (toDate.HasValue)
                {
                    query += " AND date(Timestamp) <= date(@ToDate)";
                    parameters.Add(new SqliteParameter("@ToDate", toDate.Value.ToString("yyyy-MM-dd")));
                }

                if (!string.IsNullOrEmpty(workShift))
                {
                    query += " AND WorkShift LIKE @WorkShift";
                    parameters.Add(new SqliteParameter("@WorkShift", $"%{workShift}%"));
                }

                if (!string.IsNullOrEmpty(reportType))
                {
                    query += " AND ReportType = @ReportType";
                    parameters.Add(new SqliteParameter("@ReportType", reportType));
                }

                query += " ORDER BY Timestamp DESC";

                using var command = new SqliteCommand(query, connection);
                foreach (var param in parameters)
                    command.Parameters.Add(param);

                using var reader = await command.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    result.Add(new ProductionData
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Timestamp = reader["Timestamp"].ToString() ?? "",
                        Station = reader["Station"].ToString() ?? "",
                        Product_OK = Convert.ToInt32(reader["Product_OK"]),
                        Product_NG = Convert.ToInt32(reader["Product_NG"]),
                        Product_Total = Convert.ToInt32(reader["Product_Total"]),
                        Time_Complete = Convert.ToSingle(reader["Time_Complete"]),
                        Time_Delay = Convert.ToInt32(reader["Time_Delay"]),
                        Time_Stop = Convert.ToInt32(reader["Time_Stop"]),
                        Number_Stop = Convert.ToInt32(reader["Number_Stop"]),
                        Error_Code = reader["Error_Code"].ToString() ?? "",
                        Error_Text = reader["Error_Text"].ToString() ?? "",
                        WorkShift = reader["WorkShift"].ToString() ?? "",
                        ReportType = reader["ReportType"].ToString() ?? "",
                        CreatedBy = reader["CreatedBy"].ToString() ?? "",
                        Notes = reader["Notes"].ToString() ?? ""
                    });
                }
                if (result.Count == 0)
                {
                    int fakeCount = 30;
                    var rand = new Random();
                    var stations = new[] { "ST1", "ST2", "ST3", "ST4", "ST5", "ST6", "ST7", "ST8", "ST9", "ST10", "ST11", "ST12", "ST13", "ST14", "ST15", "ST16", "ST17", "ST18" };

                    // Tạo khoảng thời gian bắt đầu
                    var from = fromDate ?? DateTime.Today;
                    var to = toDate ?? from.AddDays(1);

                    // Tổng số ngày
                    int totalDays = (int)(to - from).TotalDays + 1;
                    if (totalDays < 1) totalDays = 1;

                    for (int i = 0; i < fakeCount; i++)
                    {
                        // Sinh ngày ngẫu nhiên trong khoảng
                        var fakeDay = from.AddDays(rand.Next(0, totalDays));
                        var hour = 8 + rand.Next(0, 9); // 8h-16h (ca hành chính)
                        var fakeTimestamp = new DateTime(fakeDay.Year, fakeDay.Month, fakeDay.Day, hour, 0, 0);

                        result.Add(new ProductionData
                        {
                            Timestamp = fakeTimestamp.ToString("yyyy-MM-dd HH:mm:ss"),
                            Station = stations[i % stations.Length],
                            Product_OK = 90 + rand.Next(0, 20),
                            Product_NG = rand.Next(0, 3),
                            Product_Total = 100 + rand.Next(0, 20),
                            Time_Complete = (float)Math.Round(18 + rand.NextDouble() * 10, 1),
                            Time_Delay = rand.Next(0, 5),
                            Time_Stop = rand.Next(0, 3),
                            Number_Stop = rand.Next(0, 3),
                            Error_Code = rand.NextDouble() < 0.2 ? "X" + rand.Next(10, 120) : "",
                            Error_Text = rand.NextDouble() < 0.2 ? "Lỗi mô phỏng" : "",
                            WorkShift = workShift ?? "Ca hành chính (08:00-17:00)",
                            ReportType = reportType ?? "Production",
                            CreatedBy = "FAKE",
                            Notes = "Dữ liệu fake"
                        });
                    }
                }



            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting production data: {ex.Message}");
            }

            return result;
        }
    }
}
