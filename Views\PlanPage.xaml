<UserControl x:Class="ZoomableApp.Views.PlanPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:viewmodels="clr-namespace:ZoomableApp.ViewModels"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">
    
    <!-- DataContext will be set from MainWindow -->
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*" MinHeight="250" MaxHeight="500"/>
            <RowDefinition Height="*" MinHeight="200" MaxHeight="400"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header Panel -->
        <Border Grid.Row="0" Padding="20,15" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Vertical">
                    <TextBlock Text="📋 Kế hoạch Sản xuất" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               Foreground="White" 
                               Margin="0,0,0,5"/>
                    <TextBlock Text="Quản lý và theo dõi kế hoạch sản xuất từ file Excel" 
                               FontSize="16" 
                               Foreground="#BDC3C7"/>
                </StackPanel>
                
                <Button Grid.Column="1"
                        Command="{Binding RefreshCommand}"
                        Background="#3498DB"
                        Foreground="White"
                        FontSize="16"
                        Style="{StaticResource Modern3DButtonStyle}">
                    🔄 Làm mới
                </Button>
            </Grid>
        </Border>

        <!-- File Info Panel -->
        <Border Grid.Row="1" BorderBrush="#BDC3C7" BorderThickness="1" 
                CornerRadius="5" Padding="15" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Foreground="White" Text="📁 File Excel:" FontWeight="SemiBold" FontSize="16" Margin="0,0,0,5"/>
                    <TextBlock Text="{Binding ExcelFilePath}" FontSize="16" Foreground="White"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1">
                    <TextBlock Foreground="White" Text="{Binding ExcelFileStatus}" FontWeight="SemiBold" FontSize="16" Margin="0,0,0,5"/>
                    <TextBlock Text="{Binding ExcelRowCount}" FontSize="16" Foreground="#27AE60"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2">
                    <TextBlock Foreground="White" Text="🕒 Thời gian:" FontWeight="SemiBold" FontSize="16" Margin="0,0,0,5"/>
                    <TextBlock Text="{Binding LastUpdated}" FontSize="16" Foreground="#8E44AD"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Kế hoạch Tháng -->
        <Grid Grid.Row="2" Margin="10,0,10,10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header Kế hoạch Tháng -->
            <Border Grid.Row="0" Background="#CC0080FF" Padding="15,8" CornerRadius="5,5,0,0">
                <TextBlock Text="📅 Kế hoạch Tháng"
                           Foreground="White"
                           FontSize="18"
                           FontWeight="SemiBold"/>
            </Border>

            <!-- DataGrid Kế hoạch Tháng -->
            <Border Grid.Row="1" BorderBrush="#BDC3C7" BorderThickness="1" CornerRadius="0,0,5,5">
            <ScrollViewer HorizontalScrollBarVisibility="Auto"
                          VerticalScrollBarVisibility="Auto">
                <DataGrid x:Name="MonthlyPlanDataGrid"
                          ItemsSource="{Binding MonthlyPlanData}"
                          AutoGenerateColumns="True"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          FontSize="16"
                          RowHeight="35"
                          MinWidth="600"
                          MinHeight="200"
                          HorizontalAlignment="Stretch"
                          VerticalAlignment="Stretch">
                
                <DataGrid.Style>
                    <Style TargetType="DataGrid">
                        <Setter Property="Background" Value="White"/>
                        <Setter Property="BorderThickness" Value="0"/>
                        <Setter Property="RowBackground" Value="White"/>
                        <Setter Property="AlternatingRowBackground" Value="#F8F9FA"/>
                        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
                        <Setter Property="HorizontalGridLinesBrush" Value="#E9ECEF"/>
                    </Style>
                </DataGrid.Style>
                
                <DataGrid.ColumnHeaderStyle>
                    <Style TargetType="DataGridColumnHeader">
                        <Setter Property="Background" Value="#4BA5FF"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="FontWeight" Value="SemiBold"/>
                        <Setter Property="FontSize" Value="16"/>
                        <Setter Property="Padding" Value="12,10"/>
                        <Setter Property="BorderThickness" Value="0,0,1,0"/>
                        <Setter Property="BorderBrush" Value="#34495E"/>
                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                    </Style>
                </DataGrid.ColumnHeaderStyle>
                
                <DataGrid.CellStyle>
                    <Style TargetType="DataGridCell">
                        <Setter Property="BorderThickness" Value="0"/>
                        <Setter Property="Padding" Value="12,8"/>
                        <Setter Property="FontSize" Value="16"/>
                        <Style.Triggers>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="#3498DB"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.CellStyle>
                
                <DataGrid.RowStyle>
                    <Style TargetType="DataGridRow">
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E8F4FD"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.RowStyle>
            </DataGrid>
            </ScrollViewer>
        </Border>
        </Grid>

        <!-- Kế hoạch Ngày -->
        <Grid Grid.Row="3" Margin="10,0,10,10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header Kế hoạch Ngày -->
            <Border Grid.Row="0" Background="#CC27AE60" Padding="15,8" CornerRadius="5,5,0,0">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="📋 Kế hoạch Hôm nay"
                               Foreground="White"
                               FontSize="18"
                               FontWeight="SemiBold"
                               VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding TodayDateText}"
                               Foreground="#E8F5E8"
                               FontSize="16"
                               Margin="10,0,0,0"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- DataGrid Kế hoạch Ngày -->
            <Border Grid.Row="1" BorderBrush="#BDC3C7" BorderThickness="1" CornerRadius="0,0,5,5">
                <ScrollViewer HorizontalScrollBarVisibility="Auto"
                              VerticalScrollBarVisibility="Auto">
                    <DataGrid x:Name="DailyPlanDataGrid"
                              ItemsSource="{Binding DailyPlanData}"
                              AutoGenerateColumns="True"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              IsReadOnly="True"
                              GridLinesVisibility="Horizontal"
                              HeadersVisibility="Column"
                              FontSize="16"
                              RowHeight="35"
                              MinWidth="600"
                              MinHeight="150"
                              HorizontalAlignment="Stretch"
                              VerticalAlignment="Stretch">

                        <DataGrid.Style>
                            <Style TargetType="DataGrid">
                                <Setter Property="Background" Value="White"/>
                                <Setter Property="BorderThickness" Value="0"/>
                                <Setter Property="RowBackground" Value="White"/>
                                <Setter Property="AlternatingRowBackground" Value="#F8F9FA"/>
                                <Setter Property="GridLinesVisibility" Value="Horizontal"/>
                                <Setter Property="HorizontalGridLinesBrush" Value="#E9ECEF"/>
                            </Style>
                        </DataGrid.Style>

                        <DataGrid.ColumnHeaderStyle>
                            <Style TargetType="DataGridColumnHeader">
                                <Setter Property="Background" Value="#27AE60"/>
                                <Setter Property="Foreground" Value="White"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                                <Setter Property="FontSize" Value="16"/>
                                <Setter Property="Padding" Value="12,10"/>
                                <Setter Property="BorderThickness" Value="0,0,1,0"/>
                                <Setter Property="BorderBrush" Value="#229954"/>
                                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                            </Style>
                        </DataGrid.ColumnHeaderStyle>

                        <DataGrid.CellStyle>
                            <Style TargetType="DataGridCell">
                                <Setter Property="BorderThickness" Value="0"/>
                                <Setter Property="Padding" Value="12,8"/>
                                <Setter Property="FontSize" Value="16"/>
                                <Style.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="#58D68D"/>
                                        <Setter Property="Foreground" Value="White"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </DataGrid.CellStyle>

                        <DataGrid.RowStyle>
                            <Style TargetType="DataGridRow">
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#E8F8F5"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </DataGrid.RowStyle>
                    </DataGrid>
                </ScrollViewer>
            </Border>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="4" Background="#34495E" Padding="15,10" Margin="0,10,0,0">
            <TextBlock Text="{Binding PlanStatus}" 
                       Foreground="White" 
                       FontSize="16"
                       FontWeight="Medium"/>
        </Border>
    </Grid>
</UserControl>
