using System;
using System.IO;
using System.Diagnostics;
using ClosedXML.Excel;
using ZoomableApp.Models;

namespace ZoomableApp.Services
{
    public class IdleTimeExcelService : BaseExcelService
    {
        private readonly string _filePath;

        public IdleTimeExcelService(string filePath)
        {
            _filePath = filePath;
        }

        /// <summary>
        /// Đọc dữ liệu idle time từ file Excel
        /// File Excel có 2 cột: Day và Plans
        /// </summary>
        /// <returns>IdleTimeData với thông tin idle time cho ngày hôm nay và tháng hiện tại</returns>
        public IdleTimeData ReadIdleTimeData()
        {
            var idleTimeData = new IdleTimeData();

            try
            {
                if (!IsValidExcelFile(_filePath))
                {
                    Debug.WriteLine($"IdleTimeExcelService: File không tồn tại: {_filePath}");
                    return GetDefaultIdleTimeData();
                }

                using var workbook = OpenWorkbook(_filePath);
                var worksheet = workbook.Worksheet(1); // Lấy sheet đầu tiên

                // Tìm cột Day và Plans sử dụng base method
                int dayColumn = FindColumnByHeader(worksheet, new[] { "day", "ngày" });
                int plansColumn = FindColumnByHeader(worksheet, new[] { "plans", "kế hoạch", "giờ" });

                if (dayColumn == -1 || plansColumn == -1)
                {
                    Debug.WriteLine("IdleTimeExcelService: Không tìm thấy cột Day hoặc Plans trong file Excel");
                    return GetDefaultIdleTimeData();
                }

                // Đọc dữ liệu từ các row
                var today = DateTime.Today;
                var currentMonth = new DateTime(today.Year, today.Month, 1);
                var nextMonth = currentMonth.AddMonths(1);

                double todayIdleHours = 0;
                double monthlyIdleHours = 0;

                // Bắt đầu từ row 2 (sau header)
                var lastRow = worksheet.LastRowUsed()?.RowNumber() ?? 1;
                for (int row = 2; row <= lastRow; row++)
                {
                    var dayCell = worksheet.Cell(row, dayColumn);
                    var plansCell = worksheet.Cell(row, plansColumn);

                    // Thử parse ngày
                    DateTime rowDate;
                    if (dayCell.TryGetValue(out DateTime dateValue))
                    {
                        rowDate = dateValue.Date;
                    }
                    else if (DateTime.TryParse(dayCell.GetString(), out DateTime parsedDate))
                    {
                        rowDate = parsedDate.Date;
                    }
                    else
                    {
                        continue; // Bỏ qua row này nếu không parse được ngày
                    }

                    // Thử parse số giờ
                    double hours = 0;
                    if (plansCell.TryGetValue(out double hoursValue))
                    {
                        hours = hoursValue;
                    }
                    else if (double.TryParse(plansCell.GetString(), out double parsedHours))
                    {
                        hours = parsedHours;
                    }

                    // Kiểm tra nếu là ngày hôm nay
                    if (rowDate == today)
                    {
                        todayIdleHours = hours;
                    }

                    // Kiểm tra nếu trong tháng hiện tại
                    if (rowDate >= currentMonth && rowDate < nextMonth)
                    {
                        monthlyIdleHours += hours;
                    }
                }

                // Cập nhật IdleTimeData
                idleTimeData.DailyAllowedIdleTime = TimeSpan.FromHours(todayIdleHours);
                idleTimeData.MonthlyAllowedIdleTime = TimeSpan.FromHours(monthlyIdleHours);

                Debug.WriteLine($"IdleTimeExcelService: Đọc thành công - Hôm nay: {todayIdleHours}h, Tháng này: {monthlyIdleHours}h");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"IdleTimeExcelService: Lỗi khi đọc file Excel: {ex.Message}");
                return GetDefaultIdleTimeData();
            }

            return idleTimeData;
        }

        /// <summary>
        /// Trả về dữ liệu mặc định khi không đọc được từ Excel
        /// </summary>
        private IdleTimeData GetDefaultIdleTimeData()
        {
            return new IdleTimeData
            {
                DailyAllowedIdleTime = TimeSpan.FromHours(2),
                MonthlyAllowedIdleTime = TimeSpan.FromHours(40),
                DailyUsedIdleTime = TimeSpan.Zero,
                MonthlyUsedIdleTime = TimeSpan.Zero
            };
        }

        /// <summary>
        /// Kiểm tra xem file Excel có tồn tại và có thể đọc được không
        /// </summary>
        public bool IsFileAccessible()
        {
            return IsValidExcelFile(_filePath);
        }
    }
}
