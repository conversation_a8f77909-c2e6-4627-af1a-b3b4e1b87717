# 🏗️ PLC & DASHBOARD ARCHITECTURE GUIDE

## 📋 TỔNG QUAN KIẾN TRÚC

### 🎯 **MỤC TIÊU ĐÃ ĐẠT ĐƯỢC:**
✅ **Trừu tượng hóa PLC**: Interface `IPlcService` cho phép chuyển đổi giữa Real/Mock mode  
✅ **Dependency Injection**: ServiceContainer quản lý dependencies  
✅ **Configuration-driven**: Chuyển đổi mode qua appsettings.json  
✅ **Mock Service**: Dữ liệu giả lập realistic cho test/demo  
✅ **Dashboard Independence**: UI không phụ thuộc vào PLC implementation cụ thể  

### 🔧 **KIẾN TRÚC HIỆN TẠI:**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Dashboard     │    │   Service        │    │   PLC Layer     │
│   (UI/ViewModel)│◄──►│   Container      │◄──►│   (Real/Mock)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌────────▼────────┐              │
         │              │ Configuration   │              │
         │              │ Service         │              │
         │              └─────────────────┘              │
         │                                               │
         ▼                                               ▼
┌─────────────────┐                           ┌─────────────────┐
│ DashboardData   │                           │ IPlcService     │
│ Service         │                           │ ├─Real          │
└─────────────────┘                           │ └─Mock          │
                                              └─────────────────┘
```

## 🚀 HƯỚNG DẪN SỬ DỤNG

### 1️⃣ **CHUYỂN ĐỔI GIỮA REAL/MOCK MODE**

#### **Cách 1: Thông qua appsettings.json**
```json
{
  "PlcSettings": {
    "Mode": "Mock",  // "Real" | "Mock"
    "MockDataUpdateInterval": 2000,
    "EnableRandomVariation": true,
    "AutoConnectOnStartup": true
  }
}
```

#### **Cách 2: Thông qua code (nếu cần)**
```csharp
// Trong MainWindow.xaml.cs hoặc startup code
var plcMode = PlcMode.Mock; // hoặc PlcMode.Real
ServiceContainer.Initialize(plcMode);
```

### 2️⃣ **THÊM PLC MỚI**

#### **Bước 1: Cập nhật plc_configs.json**
```json
{
  "PlcConfigs": [
    {
      "Id": "PLC_NEW",
      "Name": "PLC Mới",
      "IpAddress": "*************",
      "Port": 4999,
      "IsEnabled": true
    }
  ]
}
```

#### **Bước 2: Không cần thay đổi code!**
- ServiceContainer tự động tạo Mock/Real service dựa trên config
- Dashboard tự động nhận dữ liệu từ PLC mới

### 3️⃣ **THÊM REGISTER MỚI**

#### **Bước 1: Cập nhật PlcEnum.cs**
```csharp
public enum PlcDeviceAddress
{
    // Existing registers...
    NewRegister_Temperature = 50,
    NewRegister_Pressure = 51
}
```

#### **Bước 2: Cập nhật plc_map.json**
```json
{
  "NewRegister_Temperature": {
    "Address": "D2000",
    "DataType": "WORD",
    "Description": "Temperature sensor"
  }
}
```

#### **Bước 3: Cập nhật MockPlcService (nếu cần mock data)**
```csharp
// Trong GetMockValue method
PlcDeviceAddress.NewRegister_Temperature => _random.Next(20, 80), // 20-80°C
```

## 🧪 TESTING & DEMO

### **Mock Mode Benefits:**
- ✅ **Không cần PLC thật**: Test dashboard mà không cần hardware
- ✅ **Dữ liệu realistic**: Mock service tạo dữ liệu giống thật
- ✅ **Random variation**: Dữ liệu thay đổi theo thời gian
- ✅ **Error simulation**: Có thể simulate các trạng thái lỗi
- ✅ **Fast development**: Phát triển UI nhanh chóng

### **Real Mode Benefits:**
- ✅ **Production ready**: Kết nối PLC thật trong sản xuất
- ✅ **Real-time data**: Dữ liệu thực từ thiết bị
- ✅ **Hardware validation**: Kiểm tra tương tác với thiết bị thật

## 📊 DASHBOARD INTEGRATION

### **Cách Dashboard sử dụng PLC Service:**
```csharp
// Dashboard không biết đang dùng Real hay Mock
public class DashboardDataService
{
    private readonly IPlcService _plcService; // Interface, không phải concrete class
    
    public async Task<ProductionData> GetDataAsync()
    {
        // Gọi interface, ServiceContainer tự động inject đúng implementation
        var result = await _plcService.ReadAsync(PlcDeviceAddress.Product_OK);
        return ProcessData(result);
    }
}
```

### **Cách MainWindow khởi tạo:**
```csharp
// MainWindow chỉ cần initialize ServiceContainer
var plcMode = ConfigurationService.GetPlcMode(); // Đọc từ config
ServiceContainer.Initialize(plcMode);

// Lấy service từ container
_dashboardDataService = ServiceContainer.GetService<DashboardDataService>();
```

## 🔧 TROUBLESHOOTING

### **Vấn đề thường gặp:**

1. **Dashboard không hiển thị dữ liệu**
   - ✅ Kiểm tra PlcSettings.Mode trong appsettings.json
   - ✅ Kiểm tra ServiceContainer đã được initialize chưa
   - ✅ Kiểm tra Mock service có đang chạy không

2. **Không kết nối được PLC thật**
   - ✅ Đổi Mode về "Mock" để test dashboard trước
   - ✅ Kiểm tra IP address và port trong plc_configs.json
   - ✅ Kiểm tra network connectivity

3. **Mock data không thay đổi**
   - ✅ Kiểm tra MockDataUpdateInterval trong config
   - ✅ Kiểm tra EnableRandomVariation = true

## 📈 PERFORMANCE & MONITORING

### **Debug Information:**
```csharp
// Kiểm tra services đã đăng ký
var diagnostics = ServiceContainer.GetDiagnosticInfo();
foreach (var kvp in diagnostics)
{
    Debug.WriteLine($"{kvp.Key}: {kvp.Value}");
}

// Kiểm tra PLC mode hiện tại
var currentMode = ConfigurationService.GetPlcMode();
Debug.WriteLine($"Current PLC Mode: {currentMode}");
```

### **Monitoring Mock Service:**
- Mock service tự động log các hoạt động vào Debug output
- Có thể theo dõi data updates qua Debug.WriteLine
- Timer updates mỗi 2 giây (configurable)

## 🎯 BEST PRACTICES

### **DO's:**
✅ Luôn sử dụng IPlcService interface, không dùng concrete class  
✅ Cấu hình mode qua appsettings.json  
✅ Test với Mock mode trước khi deploy Real mode  
✅ Sử dụng ServiceContainer để quản lý dependencies  
✅ Log các hoạt động PLC để debug  

### **DON'Ts:**
❌ Không hard-code PLC service type trong UI code  
❌ Không bypass ServiceContainer để tạo service trực tiếp  
❌ Không thay đổi business logic khi chuyển mode  
❌ Không quên cập nhật Mock service khi thêm register mới  

## 🚀 FUTURE ENHANCEMENTS

### **Có thể mở rộng:**
- 🔄 **Hot-reload config**: Thay đổi mode không cần restart app
- 📊 **Advanced mock scenarios**: Mock data theo timeline cụ thể
- 🔍 **PLC diagnostics**: Monitor PLC health và performance
- 🎛️ **UI mode switcher**: Chuyển mode qua UI settings
- 📝 **Mock data persistence**: Lưu mock state giữa các session

---

**📞 Support:** Nếu có vấn đề, kiểm tra Debug output và ServiceContainer diagnostics trước.
