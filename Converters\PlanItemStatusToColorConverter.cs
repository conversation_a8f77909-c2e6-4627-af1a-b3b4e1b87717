using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;
using ZoomableApp.ViewModels;

namespace ZoomableApp.Converters
{
    public class PlanItemStatusToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is PlanItemStatus status)
            {
                switch (status)
                {
                    case PlanItemStatus.NotStarted:
                        return new SolidColorBrush(Colors.Transparent); // Màu nền bình thường
                    case PlanItemStatus.Selected:
                        return new SolidColorBrush(Color.FromRgb(100, 149, 237)); // Cornflower Blue
                    case PlanItemStatus.InProgress:
                        return new SolidColorBrush(Color.FromRgb(255, 215, 0)); // Gold/Yellow
                    case PlanItemStatus.Completed:
                        return new SolidColorBrush(Color.FromRgb(50, 205, 50)); // Lime Green
                    default:
                        return new SolidColorBrush(Colors.Transparent);
                }
            }
            return new SolidColorBrush(Colors.Transparent);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class PlanItemStatusToTextColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is PlanItemStatus status)
            {
                switch (status)
                {
                    case PlanItemStatus.NotStarted:
                        return new SolidColorBrush(Colors.Black);
                    case PlanItemStatus.Selected:
                        return new SolidColorBrush(Colors.White);
                    case PlanItemStatus.InProgress:
                        return new SolidColorBrush(Colors.Black);
                    case PlanItemStatus.Completed:
                        return new SolidColorBrush(Colors.White);
                    default:
                        return new SolidColorBrush(Colors.Black);
                }
            }
            return new SolidColorBrush(Colors.Black);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
