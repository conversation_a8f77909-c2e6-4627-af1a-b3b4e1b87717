﻿using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Timers;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using Microsoft.Data.Sqlite;
using System.IO;
using ZoomableApp.Services;
using ZoomableApp.Models;
using System.ComponentModel;

public class StopTimesChartViewModel : INotifyPropertyChanged
{
    public ObservableCollection<ISeries> Series { get; set; }
    public ObservableCollection<Axis> XAxes { get; set; }
    public event PropertyChangedEventHandler PropertyChanged;
    protected virtual void OnPropertyChanged(string propertyName)
        => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));

    private int _totalStopTimes;
    private string _lastUpdated;

    private System.Timers.Timer _refreshTimer;
    private readonly Random _random = new Random();
    private readonly MockDashboardDataService _mockDataService = new MockDashboardDataService();
    private readonly ILoggerService _logger;

    public int TotalStopTimes
    {
        get => _totalStopTimes;
        set
        {
            if (_totalStopTimes != value)
            {
                _totalStopTimes = value;
                OnPropertyChanged(nameof(TotalStopTimes));
            }
        }
    }
    public string LastUpdated
    {
        get => _lastUpdated;
        set
        {
            if (_lastUpdated != value)
            {
                _lastUpdated = value;
                OnPropertyChanged(nameof(LastUpdated));
            }
        }
    }
    public StopTimesChartViewModel()
    {
        // Kiểm tra xem ServiceContainer đã được khởi tạo chưa
        if (ServiceContainer.IsRegistered<ILoggerService>())
        {
            _logger = ServiceContainer.GetService<ILoggerService>();
        }

        Series = new ObservableCollection<ISeries>();
        XAxes = new ObservableCollection<Axis>();

        InitializeTimer();
        LoadData();
    }

    private void InitializeTimer()
    {
        // Sử dụng interval từ configuration, fallback về 5 phút
        var interval = ConfigurationService.GetDashboardRefreshInterval();
        _refreshTimer = new System.Timers.Timer(interval);
        _refreshTimer.Elapsed += async (s, e) => await LoadDataAsync();
        _refreshTimer.Start();
    }

    private async void LoadData()
    {
        await LoadDataAsync();
    }

    private async Task LoadDataAsync()
    {
        var data = await Task.Run(() => GetTopStopTimes(10)); // lấy top 5 máy

        Series.Clear();
        XAxes.Clear();

        // Tạo gradient color cho columns
        var colors = new[]
        {
            SKColors.Red,        // Highest stop count
            SKColors.Orange,
            SKColors.Yellow,
            SKColors.LightGreen,
            SKColors.Green       // Lowest stop count
        };

        Series.Add(new ColumnSeries<int>
        {
            Values = data.Select(d => d.StopCount).ToArray(),
            Fill = new SolidColorPaint(SKColors.CornflowerBlue),
            Name = "Số lần dừng",

            DataLabelsPaint = new SolidColorPaint(SKColors.White),
            DataLabelsPosition = LiveChartsCore.Measure.DataLabelsPosition.Top,
            DataLabelsSize = 14
        });

        XAxes.Add(new Axis
        {
            Labels = data.Select(d => d.Station).ToArray(),
            TextSize = 12,
            LabelsPaint = new SolidColorPaint(SKColors.White)
        });

        TotalStopTimes = data.Sum(d => d.StopCount);
        LastUpdated = DateTime.Now.ToString("HH:mm:ss");
    }

    

    private (string Station, int StopCount)[] GetTopStopTimes(int topN)
    {
        var result = new List<(string Station, int StopCount)>();

        try
        {
            // Kiểm tra PLC mode hiện tại
            var plcMode = ConfigurationService.GetPlcMode();

            if (plcMode == PlcMode.Mock)
            {
                // Sử dụng MockDashboardDataService
                return _mockDataService.GetMockStopTimesData(topN);
            }

            // Thử đọc từ database cho Real mode
            var dataDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
            if (!Directory.Exists(dataDirectory))
            {
                Directory.CreateDirectory(dataDirectory);
            }

            var dbPath = Path.Combine(dataDirectory, "panaDB.db");
            var connectionString = $"Data Source={dbPath}";

            using var connection = new SqliteConnection(connectionString);
            connection.Open();

            string query = @"
                SELECT t.Station, t.Number_Stop
                FROM ProductionData t
                INNER JOIN (
                    SELECT Station, MAX(Timestamp) AS MaxTimestamp
                    FROM ProductionData
                    GROUP BY Station
                ) latest
                ON t.Station = latest.Station AND t.Timestamp = latest.MaxTimestamp
                ORDER BY t.Number_Stop DESC
                LIMIT @TopN;
            ";

            using var cmd = new SqliteCommand(query, connection);
            cmd.Parameters.AddWithValue("@TopN", topN);

            using var reader = cmd.ExecuteReader();
            while (reader.Read())
            {
                string station = reader.GetString(0);
                int stopCount = reader.GetInt32(1);
                result.Add((station, stopCount));
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"StopTimesChart: Error loading data: {ex.Message}");
        }

        // Nếu không có dữ liệu từ database, sử dụng mock data service
        if (result.Count == 0)
        {
            return _mockDataService.GetMockStopTimesData(topN);
        }

        return result.ToArray();
    }
}
