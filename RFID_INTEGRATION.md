# RFID Integration Guide

This document explains how to use HID Omnikey 5422/5122 readers with the application.

## Driver Installation
1. Download and install the official driver package from [HID Global](https://www.hidglobal.com/drivers/39910).
2. Reboot the system after installation if required.

## Application Setup
1. The project references the open source **PCSC** library to communicate with PC/SC compatible readers.
2. A new service `OmnikeyRfidReaderService` implements `IRfidReaderService` and listens for card events.
3. `LoginWindow` creates an instance of this service. When a card UID is scanned, the login form is automatically filled and the authentication process is triggered.

## Usage
- Start the application after installing the driver.
- Present an RFID card to the reader. The UID will appear in the RFID tab and login will occur automatically if the card exists in the database.

See the OmniKey Software Developer Guide for advanced details on APDU commands and reader configuration.
